#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时聊天系统模块
"""

import uuid
import json
import os
from datetime import datetime
from PIL import Image
import io

class ChatSystem:
    """实时聊天系统"""
    
    def __init__(self, db_manager, socketio):
        self.db = db_manager
        self.socketio = socketio
        self.active_users = {}  # 存储活跃用户信息 {user_id: {channel_id, socket_id}}
    
    def send_message(self, user_id, channel_id, content, message_type='text', image_path=None):
        """发送消息"""
        message_id = str(uuid.uuid4())
        
        # 获取用户在该频道已公开的身份
        revealed_identities = self.db.execute_query('''
            SELECT identity_name FROM user_channel_identities
            WHERE user_id = ? AND channel_id = ?
        ''', (user_id, channel_id))
        
        identities = [row['identity_name'] for row in revealed_identities]
        identities_json = json.dumps(identities, ensure_ascii=False)
        
        try:
            # 保存消息到数据库
            self.db.execute_query('''
                INSERT INTO messages (id, channel_id, user_id, content, message_type, image_path, identities)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (message_id, channel_id, user_id, content, message_type, image_path, identities_json))
            
            # 构建消息对象
            message = {
                'id': message_id,
                'channel_id': channel_id,
                'user_id': user_id,
                'content': content,
                'message_type': message_type,
                'image_path': image_path,
                'identities': identities,
                'timestamp': datetime.now().isoformat(),
                'is_deleted': False
            }
            
            # 通过WebSocket广播消息
            self.socketio.emit('new_message', message, room=channel_id)
            
            return True, message_id, "消息发送成功"
        except Exception as e:
            return False, None, f"发送消息失败: {e}"
    
    def get_channel_messages(self, channel_id, limit=50, before_message_id=None):
        """获取频道消息历史"""
        if before_message_id:
            # 获取指定消息之前的消息
            query = '''
                SELECT * FROM messages 
                WHERE channel_id = ? AND is_deleted = 0 AND created_at < (
                    SELECT created_at FROM messages WHERE id = ?
                )
                ORDER BY created_at DESC LIMIT ?
            '''
            params = (channel_id, before_message_id, limit)
        else:
            # 获取最新消息
            query = '''
                SELECT * FROM messages 
                WHERE channel_id = ? AND is_deleted = 0
                ORDER BY created_at DESC LIMIT ?
            '''
            params = (channel_id, limit)
        
        results = self.db.execute_query(query, params)
        
        messages = []
        for row in results:
            message = dict(row)
            # 解析身份JSON
            if message['identities']:
                try:
                    message['identities'] = json.loads(message['identities'])
                except:
                    message['identities'] = []
            else:
                message['identities'] = []
            messages.append(message)
        
        # 按时间正序返回
        return list(reversed(messages))
    
    def delete_message(self, user_id, message_id):
        """删除消息（仅消息发送者可删除）"""
        # 检查权限
        message = self.db.execute_query('''
            SELECT user_id, channel_id FROM messages WHERE id = ? AND is_deleted = 0
        ''', (message_id,))
        
        if not message or message[0]['user_id'] != user_id:
            return False, "无权限删除该消息"
        
        try:
            # 软删除消息
            self.db.execute_query('''
                UPDATE messages SET is_deleted = 1 WHERE id = ?
            ''', (message_id,))
            
            # 通知频道内其他用户
            channel_id = message[0]['channel_id']
            self.socketio.emit('message_deleted', {
                'message_id': message_id,
                'channel_id': channel_id
            }, room=channel_id)
            
            return True, "消息删除成功"
        except Exception as e:
            return False, f"删除消息失败: {e}"
    
    def upload_image(self, user_id, channel_id, image_file, upload_folder):
        """上传图片"""
        try:
            # 验证文件类型
            if not self._is_allowed_image(image_file):
                return False, None, None, "不支持的图片格式"
            
            # 生成文件名
            file_extension = image_file.filename.rsplit('.', 1)[1].lower()
            filename = f"{uuid.uuid4()}.{file_extension}"
            filepath = os.path.join(upload_folder, filename)
            
            # 确保上传目录存在
            os.makedirs(upload_folder, exist_ok=True)
            
            # 保存原图
            image_file.save(filepath)
            
            # 生成缩略图
            thumb_filename = f"thumb_{filename}"
            thumb_filepath = os.path.join(upload_folder, thumb_filename)
            self._create_thumbnail(filepath, thumb_filepath)
            
            return True, filename, thumb_filename, "图片上传成功"
        except Exception as e:
            return False, None, None, f"图片上传失败: {e}"
    
    def _is_allowed_image(self, file):
        """检查是否为允许的图片格式"""
        allowed_extensions = {'jpg', 'jpeg', 'png', 'gif'}
        return '.' in file.filename and \
               file.filename.rsplit('.', 1)[1].lower() in allowed_extensions
    
    def _create_thumbnail(self, original_path, thumb_path, size=(300, 300)):
        """创建缩略图"""
        try:
            with Image.open(original_path) as img:
                # 保持宽高比的缩略图
                img.thumbnail(size, Image.Resampling.LANCZOS)
                img.save(thumb_path, optimize=True, quality=85)
        except Exception as e:
            print(f"创建缩略图失败: {e}")
    
    def join_channel_room(self, user_id, channel_id, socket_id):
        """用户加入频道房间"""
        # 记录活跃用户
        self.active_users[user_id] = {
            'channel_id': channel_id,
            'socket_id': socket_id,
            'joined_at': datetime.now()
        }
        
        # 更新数据库中的在线状态
        self.db.execute_query('''
            UPDATE channel_members 
            SET is_online = 1 
            WHERE user_id = ? AND channel_id = ?
        ''', (user_id, channel_id))
        
        # 通知其他用户有新用户加入
        self.socketio.emit('user_joined', {
            'user_id': user_id,
            'channel_id': channel_id,
            'timestamp': datetime.now().isoformat()
        }, room=channel_id)
    
    def leave_channel_room(self, user_id, channel_id):
        """用户离开频道房间"""
        # 移除活跃用户记录
        if user_id in self.active_users:
            del self.active_users[user_id]
        
        # 更新数据库中的在线状态
        self.db.execute_query('''
            UPDATE channel_members 
            SET is_online = 0 
            WHERE user_id = ? AND channel_id = ?
        ''', (user_id, channel_id))
        
        # 通知其他用户有用户离开
        self.socketio.emit('user_left', {
            'user_id': user_id,
            'channel_id': channel_id,
            'timestamp': datetime.now().isoformat()
        }, room=channel_id)
    
    def get_online_users(self, channel_id):
        """获取频道在线用户"""
        online_users = []
        for user_id, info in self.active_users.items():
            if info['channel_id'] == channel_id:
                # 获取用户在该频道的公开身份
                identities = self.db.execute_query('''
                    SELECT identity_name FROM user_channel_identities
                    WHERE user_id = ? AND channel_id = ?
                ''', (user_id, channel_id))
                
                online_users.append({
                    'user_id': user_id,
                    'identities': [row['identity_name'] for row in identities],
                    'joined_at': info['joined_at'].isoformat()
                })
        
        return online_users
    
    def broadcast_identity_reveal(self, user_id, channel_id, identity_name):
        """广播身份公开事件"""
        self.socketio.emit('identity_revealed', {
            'user_id': user_id,
            'channel_id': channel_id,
            'identity_name': identity_name,
            'timestamp': datetime.now().isoformat()
        }, room=channel_id)
    
    def broadcast_identity_hide(self, user_id, channel_id, identity_name):
        """广播身份隐藏事件"""
        self.socketio.emit('identity_hidden', {
            'user_id': user_id,
            'channel_id': channel_id,
            'identity_name': identity_name,
            'timestamp': datetime.now().isoformat()
        }, room=channel_id)
    
    def get_message_statistics(self, channel_id, days=7):
        """获取消息统计"""
        results = self.db.execute_query('''
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as message_count,
                COUNT(DISTINCT user_id) as active_users
            FROM messages 
            WHERE channel_id = ? AND is_deleted = 0 
                AND created_at >= datetime('now', '-{} days')
            GROUP BY DATE(created_at)
            ORDER BY date
        '''.format(days), (channel_id,))
        
        return [dict(row) for row in results]
    
    def search_messages(self, channel_id, keyword, limit=20):
        """搜索消息"""
        results = self.db.execute_query('''
            SELECT * FROM messages 
            WHERE channel_id = ? AND is_deleted = 0 
                AND (content LIKE ? OR identities LIKE ?)
            ORDER BY created_at DESC LIMIT ?
        ''', (channel_id, f'%{keyword}%', f'%{keyword}%', limit))
        
        messages = []
        for row in results:
            message = dict(row)
            if message['identities']:
                try:
                    message['identities'] = json.loads(message['identities'])
                except:
                    message['identities'] = []
            else:
                message['identities'] = []
            messages.append(message)
        
        return messages
    
    def cleanup_old_messages(self, days=30):
        """清理旧消息（归档）"""
        try:
            # 这里可以实现消息归档逻辑
            # 例如将超过指定天数的消息移动到归档表
            affected = self.db.execute_query('''
                UPDATE messages 
                SET is_deleted = 1 
                WHERE created_at < datetime('now', '-{} days')
            '''.format(days))
            
            return True, f"已归档 {affected} 条消息"
        except Exception as e:
            return False, f"清理消息失败: {e}"

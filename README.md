# 多身份聊天室系统

一个支持多身份交流的实时聊天室系统，用户可以在不同频道中以不同身份进行交流，同时支持问卷调查、图片分享等功能。

## 主要功能

### 🎭 多身份系统
- 每个用户可设置最多3个身份（职业、性格、情感状况、兴趣爱好）
- 在不同频道中可选择性公开不同身份
- 支持身份的动态显示和隐藏

### 💬 实时聊天
- 基于WebSocket的实时消息传输
- 支持文本消息和图片分享
- 自动生成图片缩略图
- 消息历史记录和搜索

### 🏠 频道管理
- 三种频道类型：公开、密码保护、邀请制
- 频道成员管理和在线状态显示
- 邀请链接生成和管理
- 频道权限控制

### 📊 问卷系统
- 支持单选、多选、文本、标签四种题型
- 实时统计和数据分析
- Excel/CSV格式数据导出
- 问卷过期时间设置

### 🛡️ 安全机制
- 消息和上传频率限制
- 输入验证和内容过滤
- 审计日志记录
- 自动备份机制

### 🔧 管理后台
- 系统概览和统计分析
- 频道和用户管理
- 身份库管理
- 数据导出和备份管理

## 技术架构

- **后端**: Python Flask + Flask-SocketIO
- **数据库**: SQLite
- **前端**: Bootstrap 5 + JavaScript
- **实时通信**: WebSocket
- **图片处理**: Pillow
- **数据分析**: Pandas

## 快速开始

### 系统要求
- Python 3.8+
- Windows 10/11（推荐）
- 至少2GB可用内存
- 1GB可用磁盘空间

### 安装部署

1. **下载源码**
   ```bash
   git clone <repository-url>
   cd 多身份聊天室系统
   ```

2. **运行部署脚本**
   ```bash
   deploy.bat
   ```
   部署脚本会自动：
   - 创建Python虚拟环境
   - 安装所需依赖
   - 初始化数据库
   - 创建必要目录
   - 配置防火墙规则

3. **启动服务器**
   ```bash
   run.bat
   # 或者
   start_server.bat
   ```

4. **访问系统**
   打开浏览器访问：http://localhost:5000

### 手动安装

如果自动部署失败，可以手动安装：

```bash
# 创建虚拟环境
python -m venv venv
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt

# 创建目录
mkdir uploads uploads\images uploads\thumbnails backups logs

# 初始化数据库
python -c "from core.database import DatabaseManager; db = DatabaseManager('config.ini'); db.initialize_database()"

# 启动服务器
python app.py
```

## 配置说明

主要配置文件为 `config.ini`：

```ini
[DEFAULT]
# 数据库配置
DATABASE_PATH = data/chat_system.db

# 服务器配置
HOST = 0.0.0.0
PORT = 5000
DEBUG = False

# 上传配置
UPLOAD_FOLDER = uploads
MAX_CONTENT_LENGTH = 10485760  # 10MB
ALLOWED_EXTENSIONS = jpg,jpeg,png,gif

# 安全配置
SECRET_KEY = your-secret-key-here
COOKIE_EXPIRES = 63072000  # 2年

# 速率限制
RATE_LIMIT_MESSAGES = 20  # 每分钟消息数
RATE_LIMIT_UPLOADS = 5    # 每小时上传数

# 备份配置
BACKUP_FOLDER = backups
BACKUP_RETENTION_DAYS = 7
```

## 使用指南

### 用户操作

1. **设置身份**
   - 点击导航栏"身份管理"
   - 选择分类和具体身份
   - 最多可设置3个身份

2. **创建频道**
   - 在首页点击"创建频道"
   - 选择频道类型和设置
   - 设置成员上限（可选）

3. **加入频道**
   - 浏览公开频道列表
   - 点击频道加入
   - 输入密码（如需要）

4. **聊天交流**
   - 进入频道后可发送消息
   - 上传图片分享
   - 选择公开的身份

5. **创建问卷**
   - 在频道中点击"问卷"
   - 添加不同类型的问题
   - 设置过期时间

### 管理员操作

1. **访问管理后台**
   - 访问 `/admin` 路径
   - 查看系统概览

2. **频道管理**
   - 查看所有频道
   - 管理频道成员
   - 删除不当内容

3. **身份库管理**
   - 添加新的身份标签
   - 管理现有标签
   - 查看使用统计

4. **数据导出**
   - 导出聊天记录
   - 导出问卷数据
   - 生成统计报告

## 目录结构

```
多身份聊天室系统/
├── app.py                 # 主应用入口
├── config.ini            # 配置文件
├── requirements.txt      # Python依赖
├── deploy.bat           # 部署脚本
├── start_server.bat     # 启动脚本
├── stop_server.bat      # 停止脚本
├── core/                # 核心模块
│   ├── database.py      # 数据库管理
│   ├── user_system.py   # 用户系统
│   ├── channel_system.py # 频道系统
│   ├── chat_system.py   # 聊天系统
│   ├── survey_system.py # 问卷系统
│   ├── admin_system.py  # 管理系统
│   └── security.py      # 安全管理
├── templates/           # HTML模板
│   ├── base.html
│   ├── index.html
│   ├── chat.html
│   └── admin.html
├── static/              # 静态文件
│   ├── css/
│   └── js/
├── uploads/             # 上传文件
├── backups/             # 备份文件
├── logs/                # 日志文件
└── data/                # 数据库文件
```

## 常见问题

### Q: 端口5000被占用怎么办？
A: 修改 `config.ini` 中的 `PORT` 配置，或停止占用端口的程序。

### Q: 如何备份数据？
A: 系统会自动备份，也可以在管理后台手动创建备份。

### Q: 忘记频道密码怎么办？
A: 管理员可以在后台重置频道密码或将频道改为公开。

### Q: 如何清理旧数据？
A: 在管理后台的"安全管理"中可以清理过期数据。

### Q: 系统支持多少用户？
A: 理论上无限制，但建议单频道不超过100人以保证性能。

## 开发说明

### 添加新功能
1. 在 `core/` 目录添加新模块
2. 在 `app.py` 中注册路由
3. 更新数据库结构（如需要）
4. 添加前端界面

### 数据库结构
系统使用SQLite数据库，包含以下主要表：
- `users` - 用户信息
- `identity_tags` - 身份标签库
- `user_identities` - 用户身份
- `channels` - 频道信息
- `channel_members` - 频道成员
- `messages` - 消息记录
- `surveys` - 问卷信息
- `survey_responses` - 问卷回答
- `audit_logs` - 审计日志

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 支持

如有问题或建议，请提交 Issue 或联系开发团队。

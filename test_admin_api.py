#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试管理后台API
"""

import requests
import json

def test_admin_api():
    """测试管理后台API"""
    base_url = "http://localhost:5000"
    
    print("测试管理后台API...")
    print("=" * 50)
    
    # 测试系统概览API
    try:
        print("1. 测试系统概览API...")
        response = requests.get(f"{base_url}/api/admin/overview", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            if data.get('success'):
                print("✓ 系统概览API正常")
            else:
                print("✗ 系统概览API返回失败")
        else:
            print(f"✗ 系统概览API请求失败: {response.text}")
    except Exception as e:
        print(f"✗ 系统概览API测试异常: {e}")
    
    print()
    
    # 测试频道管理API
    try:
        print("2. 测试频道管理API...")
        response = requests.get(f"{base_url}/api/admin/channels", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            if data.get('success'):
                print("✓ 频道管理API正常")
            else:
                print("✗ 频道管理API返回失败")
        else:
            print(f"✗ 频道管理API请求失败: {response.text}")
    except Exception as e:
        print(f"✗ 频道管理API测试异常: {e}")
    
    print()
    
    # 测试身份库API
    try:
        print("3. 测试身份库API...")
        response = requests.get(f"{base_url}/api/admin/identities", timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"返回数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            if data.get('success'):
                print("✓ 身份库API正常")
            else:
                print("✗ 身份库API返回失败")
        else:
            print(f"✗ 身份库API请求失败: {response.text}")
    except Exception as e:
        print(f"✗ 身份库API测试异常: {e}")
    
    print()
    print("=" * 50)
    print("API测试完成")

if __name__ == "__main__":
    test_admin_api()

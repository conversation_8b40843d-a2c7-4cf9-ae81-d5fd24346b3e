#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库管理模块
"""

import sqlite3
import os
from datetime import datetime
import json
import uuid

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path):
        self.db_path = db_path
        self.ensure_db_directory()
    
    def ensure_db_directory(self):
        """确保数据库目录存在"""
        db_dir = os.path.dirname(self.db_path)
        if db_dir and not os.path.exists(db_dir):
            os.makedirs(db_dir)
    
    def get_connection(self):
        """获取数据库连接"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # 使结果可以通过列名访问
        return conn
    
    def init_database(self):
        """初始化数据库表结构"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # 用户表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 身份标签库表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS identity_tags (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT NOT NULL,  -- 'profession', 'personality', 'emotion', 'hobby'
                    name TEXT NOT NULL,
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(category, name)
                )
            ''')
            
            # 用户身份表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_identities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    identity_name TEXT NOT NULL,
                    category TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(user_id, identity_name, category)
                )
            ''')
            
            # 频道表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channels (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL CHECK (type IN ('public', 'password', 'invite')),
                    password_hash TEXT,
                    max_members INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    creator_id TEXT NOT NULL,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (creator_id) REFERENCES users (id)
                )
            ''')
            
            # 频道成员表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channel_members (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    joined_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_online BOOLEAN DEFAULT 0,
                    FOREIGN KEY (channel_id) REFERENCES channels (id),
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(channel_id, user_id)
                )
            ''')
            
            # 消息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id TEXT PRIMARY KEY,
                    channel_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    content TEXT NOT NULL,
                    message_type TEXT DEFAULT 'text',  -- 'text', 'image', 'system'
                    image_path TEXT,
                    identities TEXT,  -- JSON格式存储已公开身份
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    is_deleted BOOLEAN DEFAULT 0,
                    FOREIGN KEY (channel_id) REFERENCES channels (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # 用户频道身份公开记录表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_channel_identities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    channel_id TEXT NOT NULL,
                    identity_name TEXT NOT NULL,
                    revealed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (channel_id) REFERENCES channels (id),
                    UNIQUE(user_id, channel_id, identity_name)
                )
            ''')
            
            # 问卷表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS surveys (
                    id TEXT PRIMARY KEY,
                    channel_id TEXT NOT NULL,
                    title TEXT NOT NULL,
                    description TEXT,
                    questions TEXT NOT NULL,  -- JSON格式存储问题列表
                    created_by TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (channel_id) REFERENCES channels (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            # 问卷回答表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS survey_responses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    survey_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    responses TEXT NOT NULL,  -- JSON格式存储回答
                    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (survey_id) REFERENCES surveys (id),
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    UNIQUE(survey_id, user_id)
                )
            ''')
            
            # 邀请链接表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS invite_links (
                    id TEXT PRIMARY KEY,
                    channel_id TEXT NOT NULL,
                    created_by TEXT NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    max_uses INTEGER DEFAULT 0,  -- 0表示无限制
                    used_count INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (channel_id) REFERENCES channels (id),
                    FOREIGN KEY (created_by) REFERENCES users (id)
                )
            ''')
            
            # 审计日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS audit_logs (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    action TEXT NOT NULL,
                    target_type TEXT,  -- 'channel', 'message', 'survey', etc.
                    target_id TEXT,
                    details TEXT,  -- JSON格式存储详细信息
                    ip_address TEXT,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')

            # 管理员表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admins (
                    id TEXT PRIMARY KEY,
                    username TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    last_login TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # 管理员会话表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_sessions (
                    id TEXT PRIMARY KEY,
                    admin_id TEXT NOT NULL,
                    session_token TEXT UNIQUE NOT NULL,
                    expires_at TIMESTAMP NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (admin_id) REFERENCES admins (id)
                )
            ''')

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_channel_time ON messages (channel_id, created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_messages_user ON messages (user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_channel_members_channel ON channel_members (channel_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_user_identities_user ON user_identities (user_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_survey_responses_survey ON survey_responses (survey_id)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_audit_logs_user_time ON audit_logs (user_id, created_at)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_admin_sessions_token ON admin_sessions (session_token)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_admin_sessions_admin ON admin_sessions (admin_id)')

            # 插入默认管理员账户
            self._insert_default_admin(cursor)

            # 插入默认身份标签
            self._insert_default_identity_tags(cursor)
            
            conn.commit()
            print("数据库初始化完成")
            
        except Exception as e:
            conn.rollback()
            print(f"数据库初始化失败: {e}")
            raise
        finally:
            conn.close()
    
    def _insert_default_admin(self, cursor):
        """插入默认管理员账户"""
        import bcrypt
        import uuid

        # 检查是否已存在管理员
        cursor.execute('SELECT COUNT(*) FROM admins')
        admin_count = cursor.fetchone()[0]

        if admin_count == 0:
            # 创建默认管理员账户
            admin_id = str(uuid.uuid4())
            username = 'admin'
            password = 'admin123'  # 默认密码，建议首次登录后修改
            password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

            cursor.execute('''
                INSERT INTO admins (id, username, password_hash, email, is_active)
                VALUES (?, ?, ?, ?, ?)
            ''', (admin_id, username, password_hash, '<EMAIL>', 1))

            print(f"默认管理员账户已创建: 用户名={username}, 密码={password}")

    def _insert_default_identity_tags(self, cursor):
        """插入默认身份标签"""
        default_tags = [
            # 职业标签
            ('profession', '医生', '医疗健康专业人士'),
            ('profession', '教师', '教育工作者'),
            ('profession', '工程师', '技术专业人士'),
            ('profession', '艺术家', '创意工作者'),
            ('profession', '学生', '在校学习者'),
            ('profession', '律师', '法律专业人士'),
            ('profession', '商人', '商业从业者'),
            ('profession', '作家', '文字创作者'),
            
            # 性格标签
            ('personality', '外向', '喜欢社交，活泼开朗'),
            ('personality', '内向', '喜欢独处，深思熟虑'),
            ('personality', '乐观', '积极向上，充满希望'),
            ('personality', '理性', '逻辑思维，客观分析'),
            ('personality', '感性', '情感丰富，直觉敏锐'),
            ('personality', '幽默', '风趣诙谐，善于调节气氛'),
            
            # 情感状况
            ('emotion', '单身', '目前单身状态'),
            ('emotion', '恋爱中', '正在恋爱关系中'),
            ('emotion', '已婚', '已婚状态'),
            ('emotion', '复杂', '情感状况复杂'),
            
            # 兴趣爱好
            ('hobby', '阅读', '喜欢读书学习'),
            ('hobby', '运动', '热爱体育运动'),
            ('hobby', '音乐', '喜欢音乐艺术'),
            ('hobby', '旅行', '热爱旅游探索'),
            ('hobby', '游戏', '喜欢电子游戏'),
            ('hobby', '摄影', '摄影爱好者'),
            ('hobby', '烹饪', '喜欢烹饪美食'),
            ('hobby', '电影', '电影爱好者'),
        ]
        
        for category, name, description in default_tags:
            cursor.execute('''
                INSERT OR IGNORE INTO identity_tags (category, name, description)
                VALUES (?, ?, ?)
            ''', (category, name, description))
    
    def execute_query(self, query, params=None):
        """执行查询"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            if query.strip().upper().startswith('SELECT'):
                return cursor.fetchall()
            else:
                conn.commit()
                return cursor.rowcount
        finally:
            conn.close()
    
    def execute_many(self, query, params_list):
        """批量执行查询"""
        conn = self.get_connection()
        try:
            cursor = conn.cursor()
            cursor.executemany(query, params_list)
            conn.commit()
            return cursor.rowcount
        finally:
            conn.close()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理后台系统模块
"""

import json
import pandas as pd
from datetime import datetime, timedelta
import os

class AdminSystem:
    """管理后台系统"""
    
    def __init__(self, db_manager):
        self.db = db_manager
    
    def get_system_overview(self):
        """获取系统概览统计"""
        try:
            # 用户统计
            user_stats = self.db.execute_query('''
                SELECT 
                    COUNT(*) as total_users,
                    COUNT(CASE WHEN last_active >= datetime('now', '-1 day') THEN 1 END) as active_today,
                    COUNT(CASE WHEN last_active >= datetime('now', '-7 days') THEN 1 END) as active_week
                FROM users
            ''')[0]
            
            # 频道统计
            channel_stats = self.db.execute_query('''
                SELECT 
                    COUNT(*) as total_channels,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_channels,
                    COUNT(CASE WHEN type = 'public' THEN 1 END) as public_channels,
                    COUNT(CASE WHEN type = 'password' THEN 1 END) as password_channels,
                    COUNT(CASE WHEN type = 'invite' THEN 1 END) as invite_channels
                FROM channels
            ''')[0]
            
            # 消息统计
            message_stats = self.db.execute_query('''
                SELECT 
                    COUNT(*) as total_messages,
                    COUNT(CASE WHEN created_at >= datetime('now', '-1 day') THEN 1 END) as messages_today,
                    COUNT(CASE WHEN message_type = 'image' THEN 1 END) as image_messages
                FROM messages WHERE is_deleted = 0
            ''')[0]
            
            # 问卷统计
            survey_stats = self.db.execute_query('''
                SELECT 
                    COUNT(*) as total_surveys,
                    COUNT(CASE WHEN is_active = 1 THEN 1 END) as active_surveys,
                    (SELECT COUNT(*) FROM survey_responses) as total_responses
                FROM surveys
            ''')[0]
            
            return {
                'users': dict(user_stats),
                'channels': dict(channel_stats),
                'messages': dict(message_stats),
                'surveys': dict(survey_stats),
                'updated_at': datetime.now().isoformat()
            }
        except Exception as e:
            print(f"获取系统概览失败: {e}")
            return None
    
    def get_all_channels(self, include_deleted=False):
        """获取所有频道列表"""
        if include_deleted:
            query = '''
                SELECT c.*, 
                       (SELECT COUNT(*) FROM channel_members WHERE channel_id = c.id) as member_count,
                       (SELECT COUNT(*) FROM messages WHERE channel_id = c.id AND is_deleted = 0) as message_count
                FROM channels c
                ORDER BY c.created_at DESC
            '''
        else:
            query = '''
                SELECT c.*, 
                       (SELECT COUNT(*) FROM channel_members WHERE channel_id = c.id) as member_count,
                       (SELECT COUNT(*) FROM messages WHERE channel_id = c.id AND is_deleted = 0) as message_count
                FROM channels c
                WHERE c.is_active = 1
                ORDER BY c.created_at DESC
            '''
        
        results = self.db.execute_query(query)
        return [dict(row) for row in results]
    
    def get_channel_details(self, channel_id):
        """获取频道详细信息"""
        # 基本信息
        channel_info = self.db.execute_query('''
            SELECT * FROM channels WHERE id = ?
        ''', (channel_id,))
        
        if not channel_info:
            return None
        
        channel = dict(channel_info[0])
        
        # 成员列表
        members = self.db.execute_query('''
            SELECT cm.user_id, cm.joined_at, cm.is_online,
                   GROUP_CONCAT(uci.identity_name) as identities
            FROM channel_members cm
            LEFT JOIN user_channel_identities uci ON cm.user_id = uci.user_id AND cm.channel_id = uci.channel_id
            WHERE cm.channel_id = ?
            GROUP BY cm.user_id, cm.joined_at, cm.is_online
            ORDER BY cm.joined_at
        ''', (channel_id,))
        
        channel['members'] = []
        for member in members:
            member_info = dict(member)
            if member_info['identities']:
                member_info['identities'] = member_info['identities'].split(',')
            else:
                member_info['identities'] = []
            channel['members'].append(member_info)
        
        # 消息统计
        message_stats = self.db.execute_query('''
            SELECT 
                COUNT(*) as total_messages,
                COUNT(CASE WHEN created_at >= datetime('now', '-1 day') THEN 1 END) as messages_today,
                COUNT(CASE WHEN created_at >= datetime('now', '-7 days') THEN 1 END) as messages_week,
                COUNT(CASE WHEN message_type = 'image' THEN 1 END) as image_messages
            FROM messages 
            WHERE channel_id = ? AND is_deleted = 0
        ''', (channel_id,))
        
        channel['message_stats'] = dict(message_stats[0]) if message_stats else {}
        
        return channel
    
    def manage_identity_tags(self, action, category=None, name=None, description=None, tag_id=None):
        """管理身份标签"""
        if action == 'list':
            # 获取所有标签
            if category:
                results = self.db.execute_query('''
                    SELECT *, (SELECT COUNT(*) FROM user_identities WHERE identity_name = it.name AND category = it.category AND is_active = 1) as usage_count
                    FROM identity_tags it WHERE category = ? ORDER BY name
                ''', (category,))
            else:
                results = self.db.execute_query('''
                    SELECT *, (SELECT COUNT(*) FROM user_identities WHERE identity_name = it.name AND category = it.category AND is_active = 1) as usage_count
                    FROM identity_tags it ORDER BY category, name
                ''')
            return [dict(row) for row in results]
        
        elif action == 'add':
            # 添加新标签
            if not all([category, name]):
                return False, "分类和名称不能为空"
            
            try:
                self.db.execute_query('''
                    INSERT INTO identity_tags (category, name, description)
                    VALUES (?, ?, ?)
                ''', (category, name, description or ''))
                return True, "标签添加成功"
            except Exception as e:
                if "UNIQUE constraint failed" in str(e):
                    return False, "该标签已存在"
                return False, f"添加标签失败: {e}"
        
        elif action == 'update':
            # 更新标签
            if not tag_id:
                return False, "标签ID不能为空"
            
            try:
                self.db.execute_query('''
                    UPDATE identity_tags 
                    SET name = COALESCE(?, name), description = COALESCE(?, description)
                    WHERE id = ?
                ''', (name, description, tag_id))
                return True, "标签更新成功"
            except Exception as e:
                return False, f"更新标签失败: {e}"
        
        elif action == 'delete':
            # 删除标签
            if not tag_id:
                return False, "标签ID不能为空"
            
            # 检查是否有用户在使用
            usage = self.db.execute_query('''
                SELECT COUNT(*) as count FROM user_identities ui
                JOIN identity_tags it ON ui.identity_name = it.name AND ui.category = it.category
                WHERE it.id = ? AND ui.is_active = 1
            ''', (tag_id,))
            
            if usage and usage[0]['count'] > 0:
                return False, f"该标签正在被 {usage[0]['count']} 个用户使用，无法删除"
            
            try:
                self.db.execute_query('DELETE FROM identity_tags WHERE id = ?', (tag_id,))
                return True, "标签删除成功"
            except Exception as e:
                return False, f"删除标签失败: {e}"
        
        else:
            return False, "不支持的操作"
    
    def export_chat_data(self, channel_id=None, format='csv', days=None):
        """导出聊天记录"""
        try:
            # 构建查询条件
            conditions = ["m.is_deleted = 0"]
            params = []
            
            if channel_id:
                conditions.append("m.channel_id = ?")
                params.append(channel_id)
            
            if days:
                conditions.append("m.created_at >= datetime('now', '-{} days')".format(days))
            
            where_clause = " AND ".join(conditions)
            
            # 查询数据
            query = f'''
                SELECT 
                    m.id as message_id,
                    m.channel_id,
                    c.name as channel_name,
                    m.user_id,
                    m.content,
                    m.message_type,
                    m.identities,
                    m.created_at
                FROM messages m
                LEFT JOIN channels c ON m.channel_id = c.id
                WHERE {where_clause}
                ORDER BY m.created_at
            '''
            
            results = self.db.execute_query(query, params)
            
            if not results:
                return None, "没有找到数据"
            
            # 处理数据
            data = []
            for row in results:
                record = dict(row)
                # 解析身份JSON
                if record['identities']:
                    try:
                        identities = json.loads(record['identities'])
                        record['identities'] = '; '.join(identities)
                    except:
                        record['identities'] = ''
                else:
                    record['identities'] = ''
                data.append(record)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            # 重命名列
            df.columns = ['消息ID', '频道ID', '频道名称', '用户ID', '消息内容', '消息类型', '公开身份', '发送时间']
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if channel_id:
                filename = f"chat_export_{channel_id}_{timestamp}"
            else:
                filename = f"chat_export_all_{timestamp}"
            
            if format == 'csv':
                filename += '.csv'
            elif format == 'excel':
                filename += '.xlsx'
            else:
                return None, "不支持的导出格式"
            
            return df, filename
        
        except Exception as e:
            return None, f"导出数据失败: {e}"
    
    def get_user_activity_report(self, days=7):
        """获取用户活跃度报告"""
        try:
            # 每日活跃用户
            daily_active = self.db.execute_query('''
                SELECT 
                    DATE(last_active) as date,
                    COUNT(*) as active_users
                FROM users 
                WHERE last_active >= datetime('now', '-{} days')
                GROUP BY DATE(last_active)
                ORDER BY date
            '''.format(days))
            
            # 每日消息数
            daily_messages = self.db.execute_query('''
                SELECT 
                    DATE(created_at) as date,
                    COUNT(*) as message_count,
                    COUNT(DISTINCT user_id) as active_senders
                FROM messages 
                WHERE created_at >= datetime('now', '-{} days') AND is_deleted = 0
                GROUP BY DATE(created_at)
                ORDER BY date
            '''.format(days))
            
            # 频道活跃度
            channel_activity = self.db.execute_query('''
                SELECT 
                    c.id,
                    c.name,
                    COUNT(m.id) as message_count,
                    COUNT(DISTINCT m.user_id) as active_users,
                    COUNT(DISTINCT cm.user_id) as total_members
                FROM channels c
                LEFT JOIN messages m ON c.id = m.channel_id AND m.created_at >= datetime('now', '-{} days') AND m.is_deleted = 0
                LEFT JOIN channel_members cm ON c.id = cm.channel_id
                WHERE c.is_active = 1
                GROUP BY c.id, c.name
                ORDER BY message_count DESC
            '''.format(days))
            
            return {
                'daily_active_users': [dict(row) for row in daily_active],
                'daily_messages': [dict(row) for row in daily_messages],
                'channel_activity': [dict(row) for row in channel_activity],
                'period_days': days,
                'generated_at': datetime.now().isoformat()
            }
        
        except Exception as e:
            print(f"生成活跃度报告失败: {e}")
            return None
    
    def cleanup_system_data(self, days=30):
        """清理系统数据"""
        try:
            results = {}
            
            # 清理过期的邀请链接
            expired_invites = self.db.execute_query('''
                UPDATE invite_links 
                SET is_active = 0 
                WHERE expires_at < CURRENT_TIMESTAMP AND is_active = 1
            ''')
            results['expired_invites'] = expired_invites
            
            # 清理旧的审计日志
            old_logs = self.db.execute_query('''
                DELETE FROM audit_logs 
                WHERE created_at < datetime('now', '-{} days')
            '''.format(days * 2))  # 审计日志保留更长时间
            results['old_audit_logs'] = old_logs
            
            # 清理已删除频道的相关数据
            deleted_channel_data = self.db.execute_query('''
                DELETE FROM channel_members 
                WHERE channel_id IN (
                    SELECT id FROM channels WHERE is_active = 0 AND created_at < datetime('now', '-{} days')
                )
            '''.format(days))
            results['deleted_channel_members'] = deleted_channel_data
            
            return True, results
        
        except Exception as e:
            return False, f"清理数据失败: {e}"
    
    def get_identity_usage_stats(self):
        """获取身份使用统计"""
        try:
            # 按分类统计
            category_stats = self.db.execute_query('''
                SELECT 
                    ui.category,
                    COUNT(*) as usage_count,
                    COUNT(DISTINCT ui.user_id) as unique_users
                FROM user_identities ui
                WHERE ui.is_active = 1
                GROUP BY ui.category
                ORDER BY usage_count DESC
            ''')
            
            # 具体身份统计
            identity_stats = self.db.execute_query('''
                SELECT 
                    ui.category,
                    ui.identity_name,
                    COUNT(*) as usage_count,
                    COUNT(DISTINCT ui.user_id) as unique_users,
                    it.description
                FROM user_identities ui
                LEFT JOIN identity_tags it ON ui.identity_name = it.name AND ui.category = it.category
                WHERE ui.is_active = 1
                GROUP BY ui.category, ui.identity_name
                ORDER BY ui.category, usage_count DESC
            ''')
            
            return {
                'category_stats': [dict(row) for row in category_stats],
                'identity_stats': [dict(row) for row in identity_stats],
                'generated_at': datetime.now().isoformat()
            }
        
        except Exception as e:
            print(f"获取身份统计失败: {e}")
            return None

    def get_channels_data(self):
        """获取频道管理数据"""
        try:
            channels = self.get_all_channels()
            return {
                'channels': channels,
                'total_count': len(channels)
            }
        except Exception as e:
            print(f"获取频道数据失败: {e}")
            return {'channels': [], 'total_count': 0}

    def get_surveys_data(self):
        """获取问卷数据"""
        try:
            surveys = self.db.execute_query('''
                SELECT s.*,
                       COUNT(sr.id) as response_count,
                       u.name as creator_name
                FROM surveys s
                LEFT JOIN survey_responses sr ON s.id = sr.survey_id
                LEFT JOIN user_identities u ON s.creator_identity_id = u.id
                GROUP BY s.id
                ORDER BY s.created_at DESC
            ''')

            return {
                'surveys': [dict(row) for row in surveys],
                'total_count': len(surveys)
            }
        except Exception as e:
            print(f"获取问卷数据失败: {e}")
            return {'surveys': [], 'total_count': 0}

    def get_analytics_data(self):
        """获取数据分析"""
        try:
            # 获取用户活跃度报告
            activity_report = self.get_user_activity_report()

            # 获取身份使用统计
            identity_stats = self.get_identity_usage_stats()

            # 获取频道统计
            channel_stats = self.db.execute_query('''
                SELECT
                    c.type,
                    COUNT(*) as count,
                    AVG(member_count) as avg_members
                FROM channels c
                LEFT JOIN (
                    SELECT channel_id, COUNT(*) as member_count
                    FROM channel_members
                    GROUP BY channel_id
                ) cm ON c.id = cm.channel_id
                WHERE c.is_deleted = 0
                GROUP BY c.type
            ''')

            return {
                'activity_report': activity_report,
                'identity_stats': identity_stats,
                'channel_stats': [dict(row) for row in channel_stats]
            }
        except Exception as e:
            print(f"获取分析数据失败: {e}")
            return {}

    def export_survey_data(self, format='csv'):
        """导出问卷数据"""
        try:
            import pandas as pd
            from datetime import datetime

            # 获取问卷和回答数据
            data = self.db.execute_query('''
                SELECT
                    s.title as survey_title,
                    s.description as survey_description,
                    sq.question_text,
                    sq.question_type,
                    sr.answer_text,
                    u.name as respondent_name,
                    sr.created_at as response_time
                FROM surveys s
                JOIN survey_questions sq ON s.id = sq.survey_id
                LEFT JOIN survey_responses sr ON sq.id = sr.question_id
                LEFT JOIN user_identities u ON sr.identity_id = u.id
                ORDER BY s.id, sq.order_index, sr.created_at
            ''')

            df = pd.DataFrame([dict(row) for row in data])

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"survey_data_{timestamp}.{format}"
            filepath = os.path.join('exports', filename)

            # 确保导出目录存在
            os.makedirs('exports', exist_ok=True)

            # 导出文件
            if format == 'csv':
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
            elif format == 'excel':
                df.to_excel(filepath, index=False)

            return filepath

        except Exception as e:
            print(f"导出问卷数据失败: {e}")
            return None

    def export_user_data(self, format='csv'):
        """导出用户数据"""
        try:
            import pandas as pd
            from datetime import datetime

            # 获取用户数据
            data = self.db.execute_query('''
                SELECT
                    u.user_id,
                    u.name,
                    u.profession,
                    u.personality,
                    u.emotional_state,
                    u.interests,
                    u.created_at,
                    COUNT(DISTINCT cm.channel_id) as joined_channels,
                    COUNT(DISTINCT m.id) as message_count
                FROM user_identities u
                LEFT JOIN channel_members cm ON u.id = cm.identity_id
                LEFT JOIN messages m ON u.id = m.sender_identity_id
                GROUP BY u.id
                ORDER BY u.created_at DESC
            ''')

            df = pd.DataFrame([dict(row) for row in data])

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"user_data_{timestamp}.{format}"
            filepath = os.path.join('exports', filename)

            # 确保导出目录存在
            os.makedirs('exports', exist_ok=True)

            # 导出文件
            if format == 'csv':
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
            elif format == 'excel':
                df.to_excel(filepath, index=False)

            return filepath

        except Exception as e:
            print(f"导出用户数据失败: {e}")
            return None

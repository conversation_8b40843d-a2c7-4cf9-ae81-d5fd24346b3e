#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多身份聊天室系统 - 系统测试脚本
"""

import os
import sys
import sqlite3
from datetime import datetime

def test_database_connection():
    """测试数据库连接"""
    try:
        from core.database import DatabaseManager
        db = DatabaseManager('config.ini')
        
        # 测试连接
        result = db.execute_query("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row['name'] for row in result]
        
        expected_tables = [
            'users', 'identity_tags', 'user_identities', 'channels', 
            'channel_members', 'messages', 'surveys', 'survey_questions',
            'survey_responses', 'invite_links', 'audit_logs', 'user_channel_identities'
        ]
        
        missing_tables = [table for table in expected_tables if table not in tables]
        
        if missing_tables:
            print(f"❌ 数据库测试失败: 缺少表 {missing_tables}")
            return False
        else:
            print(f"✅ 数据库测试通过: 找到 {len(tables)} 个表")
            return True
            
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def test_core_modules():
    """测试核心模块导入"""
    modules = [
        'core.database',
        'core.user_system',
        'core.channel_system', 
        'core.chat_system',
        'core.survey_system',
        'core.admin_system',
        'core.security'
    ]
    
    failed_modules = []
    
    for module in modules:
        try:
            __import__(module)
            print(f"✅ 模块 {module} 导入成功")
        except Exception as e:
            print(f"❌ 模块 {module} 导入失败: {e}")
            failed_modules.append(module)
    
    return len(failed_modules) == 0

def test_directory_structure():
    """测试目录结构"""
    required_dirs = [
        'core',
        'templates', 
        'static',
        'static/css',
        'static/js'
    ]
    
    optional_dirs = [
        'uploads',
        'uploads/images',
        'uploads/thumbnails',
        'backups',
        'logs',
        'data'
    ]
    
    missing_required = []
    missing_optional = []
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_required.append(dir_path)
        else:
            print(f"✅ 必需目录 {dir_path} 存在")
    
    for dir_path in optional_dirs:
        if not os.path.exists(dir_path):
            missing_optional.append(dir_path)
        else:
            print(f"✅ 可选目录 {dir_path} 存在")
    
    if missing_required:
        print(f"❌ 缺少必需目录: {missing_required}")
        return False
    
    if missing_optional:
        print(f"⚠️  缺少可选目录: {missing_optional}")
        print("   这些目录会在首次运行时自动创建")
    
    return True

def test_config_file():
    """测试配置文件"""
    if not os.path.exists('config.ini'):
        print("❌ 配置文件 config.ini 不存在")
        return False
    
    try:
        import configparser
        config = configparser.ConfigParser()
        config.read('config.ini', encoding='utf-8')
        
        required_keys = [
            'DATABASE_PATH',
            'HOST', 
            'PORT',
            'SECRET_KEY',
            'UPLOAD_FOLDER'
        ]
        
        missing_keys = []
        for key in required_keys:
            if not config.has_option('DEFAULT', key):
                missing_keys.append(key)
        
        if missing_keys:
            print(f"❌ 配置文件缺少必需配置: {missing_keys}")
            return False
        else:
            print("✅ 配置文件检查通过")
            return True
            
    except Exception as e:
        print(f"❌ 配置文件读取失败: {e}")
        return False

def test_dependencies():
    """测试Python依赖"""
    required_packages = [
        ('flask', 'flask'),
        ('flask_socketio', 'flask_socketio'),
        ('bcrypt', 'bcrypt'),
        ('pillow', 'PIL'),
        ('pandas', 'pandas')
    ]

    missing_packages = []

    for package_name, import_name in required_packages:
        try:
            __import__(import_name)
            print(f"✅ 依赖包 {package_name} 已安装")
        except ImportError:
            missing_packages.append(package_name)
            print(f"❌ 依赖包 {package_name} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺少的依赖包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def test_flask_app():
    """测试Flask应用"""
    try:
        from app import app
        
        # 测试应用配置
        if not app.secret_key:
            print("❌ Flask应用缺少secret_key配置")
            return False
        
        # 测试路由注册
        routes = [rule.rule for rule in app.url_map.iter_rules()]
        expected_routes = ['/', '/chat/<channel_id>', '/admin']
        
        missing_routes = [route for route in expected_routes if route not in routes]
        
        if missing_routes:
            print(f"❌ 缺少路由: {missing_routes}")
            return False
        
        print("✅ Flask应用配置正确")
        return True
        
    except Exception as e:
        print(f"❌ Flask应用测试失败: {e}")
        return False

def create_missing_directories():
    """创建缺少的目录"""
    dirs_to_create = [
        'uploads',
        'uploads/images', 
        'uploads/thumbnails',
        'backups',
        'logs',
        'data'
    ]
    
    for dir_path in dirs_to_create:
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")
            except Exception as e:
                print(f"❌ 创建目录失败 {dir_path}: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("多身份聊天室系统 - 系统测试")
    print("=" * 50)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        ("目录结构测试", test_directory_structure),
        ("配置文件测试", test_config_file),
        ("Python依赖测试", test_dependencies),
        ("核心模块测试", test_core_modules),
        ("数据库连接测试", test_database_connection),
        ("Flask应用测试", test_flask_app)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
            else:
                print(f"测试失败: {test_name}")
        except Exception as e:
            print(f"测试异常: {test_name} - {e}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统可以正常运行。")
        print("\n启动建议:")
        print("1. 运行 python app.py 启动服务器")
        print("2. 在浏览器中访问 http://localhost:5000")
    else:
        print("⚠️  部分测试失败，请检查上述错误信息。")
        
        # 尝试修复一些问题
        print("\n尝试自动修复...")
        create_missing_directories()
        
        if not os.path.exists('data'):
            print("正在初始化数据库...")
            try:
                from core.database import DatabaseManager
                db = DatabaseManager('config.ini')
                db.initialize_database()
                print("✅ 数据库初始化完成")
            except Exception as e:
                print(f"❌ 数据库初始化失败: {e}")
    
    print("=" * 50)

if __name__ == "__main__":
    main()

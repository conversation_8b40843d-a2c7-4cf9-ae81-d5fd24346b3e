{% extends "base.html" %}

{% block title %}管理后台 - 多身份聊天室系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧导航 -->
    <div class="col-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog"></i> 管理功能
                </h5>
            </div>
            <div class="list-group list-group-flush">
                <a href="#overview" class="list-group-item list-group-item-action active" 
                   onclick="showAdminSection('overview', this)">
                    <i class="fas fa-chart-line"></i> 系统概览
                </a>
                <a href="#channels" class="list-group-item list-group-item-action" 
                   onclick="showAdminSection('channels', this)">
                    <i class="fas fa-hashtag"></i> 频道管理
                </a>
                <a href="#identities" class="list-group-item list-group-item-action" 
                   onclick="showAdminSection('identities', this)">
                    <i class="fas fa-user-tag"></i> 身份库管理
                </a>
                <a href="#surveys" class="list-group-item list-group-item-action" 
                   onclick="showAdminSection('surveys', this)">
                    <i class="fas fa-poll"></i> 问卷管理
                </a>
                <a href="#analytics" class="list-group-item list-group-item-action" 
                   onclick="showAdminSection('analytics', this)">
                    <i class="fas fa-chart-bar"></i> 数据分析
                </a>
                <a href="#export" class="list-group-item list-group-item-action" 
                   onclick="showAdminSection('export', this)">
                    <i class="fas fa-download"></i> 数据导出
                </a>
                <a href="#security" class="list-group-item list-group-item-action" 
                   onclick="showAdminSection('security', this)">
                    <i class="fas fa-shield-alt"></i> 安全管理
                </a>
                <a href="#backup" class="list-group-item list-group-item-action" 
                   onclick="showAdminSection('backup', this)">
                    <i class="fas fa-database"></i> 备份管理
                </a>
            </div>
        </div>
    </div>
    
    <!-- 右侧内容区域 -->
    <div class="col-md-9">
        <!-- 系统概览 -->
        <div id="overview-section" class="admin-section">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-line"></i> 系统概览
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row" id="systemStats">
                        <!-- 动态加载统计数据 -->
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-6">
                            <canvas id="userActivityChart" width="400" height="200"></canvas>
                        </div>
                        <div class="col-md-6">
                            <canvas id="channelStatsChart" width="400" height="200"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 频道管理 -->
        <div id="channels-section" class="admin-section" style="display: none;">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-hashtag"></i> 频道管理
                    </h5>
                    <button class="btn btn-sm btn-primary" onclick="refreshChannelList()">
                        <i class="fas fa-sync"></i> 刷新
                    </button>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>频道名称</th>
                                    <th>类型</th>
                                    <th>成员数</th>
                                    <th>消息数</th>
                                    <th>创建时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="channelTableBody">
                                <!-- 动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 身份库管理 -->
        <div id="identities-section" class="admin-section" style="display: none;">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-user-tag"></i> 身份库管理
                    </h5>
                    <button class="btn btn-sm btn-primary" onclick="showAddIdentityModal()">
                        <i class="fas fa-plus"></i> 添加身份标签
                    </button>
                </div>
                <div class="card-body">
                    <ul class="nav nav-pills mb-3" id="identityTabs">
                        <li class="nav-item">
                            <a class="nav-link active" href="#" onclick="showIdentityCategory('profession')">职业</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showIdentityCategory('personality')">性格</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showIdentityCategory('emotion')">情感状况</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="#" onclick="showIdentityCategory('hobby')">兴趣爱好</a>
                        </li>
                    </ul>
                    
                    <div id="identityTagsList">
                        <!-- 动态加载 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 问卷管理 -->
        <div id="surveys-section" class="admin-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-poll"></i> 问卷管理
                    </h5>
                </div>
                <div class="card-body">
                    <div id="surveyManagementList">
                        <!-- 动态加载 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据分析 -->
        <div id="analytics-section" class="admin-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> 数据分析
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>用户活跃度分析</h6>
                            <canvas id="userAnalyticsChart" width="400" height="300"></canvas>
                        </div>
                        <div class="col-md-6">
                            <h6>身份使用统计</h6>
                            <canvas id="identityUsageChart" width="400" height="300"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 数据导出 -->
        <div id="export-section" class="admin-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-download"></i> 数据导出
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>聊天记录导出</h6>
                            <form id="exportChatForm">
                                <div class="mb-3">
                                    <label class="form-label">选择频道</label>
                                    <select class="form-select" id="exportChannelSelect">
                                        <option value="">全部频道</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">时间范围（天）</label>
                                    <input type="number" class="form-control" id="exportDays" value="30" min="1" max="365">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">导出格式</label>
                                    <select class="form-select" id="exportFormat">
                                        <option value="csv">CSV</option>
                                        <option value="excel">Excel</option>
                                    </select>
                                </div>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-download"></i> 导出聊天记录
                                </button>
                            </form>
                        </div>
                        <div class="col-md-6">
                            <h6>问卷数据导出</h6>
                            <div id="surveyExportList">
                                <!-- 动态加载 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 安全管理 -->
        <div id="security-section" class="admin-section" style="display: none;">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt"></i> 安全管理
                    </h5>
                </div>
                <div class="card-body">
                    <div id="securityReport">
                        <!-- 动态加载安全报告 -->
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 备份管理 -->
        <div id="backup-section" class="admin-section" style="display: none;">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-database"></i> 备份管理
                    </h5>
                    <button class="btn btn-sm btn-primary" onclick="createBackup()">
                        <i class="fas fa-plus"></i> 创建备份
                    </button>
                </div>
                <div class="card-body">
                    <div id="backupList">
                        <!-- 动态加载备份列表 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加身份标签模态框 -->
<div class="modal fade" id="addIdentityTagModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> 添加身份标签
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addIdentityTagForm">
                    <div class="mb-3">
                        <label class="form-label">分类</label>
                        <select class="form-select" id="newTagCategory" required>
                            <option value="profession">职业</option>
                            <option value="personality">性格</option>
                            <option value="emotion">情感状况</option>
                            <option value="hobby">兴趣爱好</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">标签名称</label>
                        <input type="text" class="form-control" id="newTagName" required maxlength="20">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">描述</label>
                        <textarea class="form-control" id="newTagDescription" rows="3" maxlength="100"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addIdentityTag()">添加标签</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{{ url_for('static', filename='js/admin.js') }}"></script>
{% endblock %}

使用python语言（虚拟环境）


多身份聊天室系统技术规格书
一、核心功能模块
用户身份系统

基于Cookie的UUID生成机制（有效期2年）

每个用户最多可选择3个身份
其他信息可选并且管理员在后台可以添加\删除其他的信息分类： 
性格标签（可选）
情感状况（可选）
兴趣爱好（可选）


身份公开机制：
加入频道时默认公开一个身份
聊天中可随时公开其他身份（非切换）
已公开身份在消息旁显示徽章图标

频道管理系统

python
class Channel:
    id: UUID
    name: str
    type: Enum('public', 'password', 'invite')  # 频道类型
    password: str  # bcrypt加密存储
    max_members: int  # 0表示无限制
    created_at: datetime
    creator: UUID  # 管理员ID
创建流程：

管理员设置名称/类型/人数限制

系统生成唯一频道ID

密码制频道需加密存储密码

邀请制频道生成时效链接(24h)

实时聊天系统

协议：WebSocket + HTTP fallback

消息结构：

json
{
  "id": "msg_001",
  "channel": "channel_123",
  "user": "user_abc",
  "content": "Hello world!",
  "timestamp": "2025-03-15T10:30:00Z",
  "identities": ["医生", "画家"],  // 已公开身份
  "image": "/uploads/img_001_thumb.jpg" // 缩略图路径
}
图片支持(自动压缩上传)聊天界面显示缩略图，点击后查看大图： 
格式：JPG/PNG/GIF
大小：≤10MB 


问卷系统

问卷结构：
python
class Survey:
    id: UUID
    channel: UUID
    questions: List[dict]  # 问题列表
    created_at: datetime
    expires_at: datetime  # 可选过期时间
问题类型：

python
QUESTION_TYPES = {
    'SINGLE': '单选',
    'MULTI': '多选',
    'TEXT': '文本输入',
    'LABEL': '信息标签'  // 无需回答
}
回答统计：

实时显示完成率

图表可视化（饼图/柱状图）

文本回答词云分析

管理后台功能

频道管理：

创建/编辑/删除频道

查看在线成员

导出聊天记录(CSV/JSON)

身份库管理：

添加/删除职业标签

管理性格/情感标签

数据分析：

问卷结果导出(Excel)

用户活跃度统计


三、API端点设计
用户端点

POST /api/init：初始化用户Cookie

PUT /api/identity：管理身份卡

频道端点

POST /api/channel：创建频道

GET /api/channel/{id}/join：加入频道

DELETE /api/channel/{id}：删除频道

聊天端点

WS /ws/chat/{channel_id}：WebSocket连接

POST /api/upload：图片上传

问卷端点

POST /api/survey：发布问卷

GET /api/survey/{id}/responses：获取回答

POST /api/survey/{id}/submit：提交回答

管理端点

GET /admin/channels：频道列表

GET /admin/export?type=chat：导出聊天记录

PUT /admin/tags：管理身份标签

四、安全规范
Cookie安全

python
response.set_cookie(
    'user_id',
     value=user_id,
     httponly=True,
     secure=True,
     samesite='Lax',
     max_age=63072000  # 2年
)
数据验证

所有输入参数Schema验证

图片文件头校验(Pillow)

SQL注入防护(参数化查询)

速率限制

普通用户：20条/分钟

上传功能：5次/小时

五、部署要求
环境

Python 3.10+

依赖库：

requirements.txt
Flask==3.0.2
Flask-SocketIO==5.3.6
Pillow==10.2.0
bcrypt==4.1.2
pandas==2.2.1
目录结构

text
/project
  ├── app.py          # 主入口
  ├── config.ini      # 配置文件
  ├── /core           # 核心模块
  ├── /templates      # 前端模板
  ├── /static         # 静态资源
  ├── /uploads        # 图片存储
  └── /database       # SQLite数据库
Windows部署

一键启动脚本：start.bat

bat
@echo off
python -m venv venv
call venv\Scripts\activate
pip install -r requirements.txt
python app.py
六、补充需求
消息持久化

自动保存最近10万条消息

归档超过30天的频道数据

通知系统

@提及通知

问卷发布全局提醒

新成员加入通知

审计日志

关键操作记录：

python
class AuditLog:
    id: UUID
    user: UUID
    action: str  # "CREATE_CHANNEL", "DELETE_MESSAGE"等
    target: UUID  # 操作对象ID
    timestamp: datetime
备份机制
每日凌晨自动备份
保留最近7天备份
手动备份触发接口


#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多身份聊天室系统 - 主应用入口
"""

import os
import configparser
from flask import Flask, render_template, request, jsonify, session, redirect, url_for
from flask_socketio import Socket<PERSON>, emit, join_room, leave_room
import uuid
import sqlite3
from datetime import datetime, timedelta
import bcrypt
import json
from werkzeug.utils import secure_filename
from PIL import Image
import io
import base64

# 导入核心模块
from core.database import DatabaseManager
from core.user_system import UserSystem
from core.channel_system import ChannelSystem
from core.chat_system import ChatSystem
from core.survey_system import SurveySystem
from core.admin_system import AdminSystem
from core.security import SecurityManager
from core.admin_auth import AdminAuth, require_admin_login

# 读取配置
config = configparser.ConfigParser()
config.read('config.ini', encoding='utf-8')

# 创建Flask应用
app = Flask(__name__)
app.config['SECRET_KEY'] = config.get('DEFAULT', 'SECRET_KEY')
app.config['UPLOAD_FOLDER'] = config.get('DEFAULT', 'UPLOAD_FOLDER')
app.config['MAX_CONTENT_LENGTH'] = config.getint('DEFAULT', 'MAX_CONTENT_LENGTH')

# 创建SocketIO实例
socketio = SocketIO(app, cors_allowed_origins="*", async_mode='threading')

# 初始化系统组件
db_manager = DatabaseManager(config.get('DEFAULT', 'DATABASE_PATH'))
user_system = UserSystem(db_manager)
channel_system = ChannelSystem(db_manager)
chat_system = ChatSystem(db_manager, socketio)
survey_system = SurveySystem(db_manager)
admin_system = AdminSystem(db_manager)
security_manager = SecurityManager(config)
admin_auth = AdminAuth(db_manager)

# 允许的文件扩展名
ALLOWED_EXTENSIONS = set(config.get('DEFAULT', 'ALLOWED_EXTENSIONS').split(','))

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.before_request
def before_request():
    """请求前处理"""
    # 初始化用户
    if 'user_id' not in session:
        user_id = str(uuid.uuid4())
        session['user_id'] = user_id
        session.permanent = True
        app.permanent_session_lifetime = timedelta(seconds=config.getint('DEFAULT', 'COOKIE_MAX_AGE'))
        
        # 在数据库中创建用户记录
        user_system.create_user(user_id)

@app.route('/')
def index():
    """主页"""
    return render_template('index.html')

@app.route('/chat/<channel_id>')
def chat(channel_id):
    """聊天页面"""
    user_id = session.get('user_id')
    
    # 检查用户是否可以访问频道
    if not channel_system.can_access_channel(user_id, channel_id):
        return redirect(url_for('index'))
    
    channel_info = channel_system.get_channel_info(channel_id)
    user_identities = user_system.get_user_identities(user_id)
    
    return render_template('chat.html', 
                         channel=channel_info, 
                         user_identities=user_identities)

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """管理员登录"""
    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        if not username or not password:
            return render_template('admin_login.html', error='请输入用户名和密码')

        success, result = admin_auth.verify_password(username, password)

        if success:
            # 创建会话
            session_token = admin_auth.create_session(result['id'])
            if session_token:
                session['admin_session_token'] = session_token
                return redirect(url_for('admin'))
            else:
                return render_template('admin_login.html', error='创建会话失败')
        else:
            return render_template('admin_login.html', error=result)

    return render_template('admin_login.html')

@app.route('/admin/logout')
def admin_logout():
    """管理员登出"""
    session_token = session.get('admin_session_token')
    if session_token:
        admin_auth.logout(session_token)
        session.pop('admin_session_token', None)
    return redirect(url_for('admin_login'))

@app.route('/admin')
@require_admin_login
def admin():
    """管理后台"""
    return render_template('admin.html')

# 管理后台API路由
@app.route('/api/admin/overview')
@require_admin_login
def admin_overview():
    """获取系统概览数据"""
    try:
        overview_data = admin_system.get_system_overview()
        if overview_data:
            return jsonify({
                'success': True,
                'stats': overview_data,
                'charts': {
                    'daily_messages': overview_data.get('daily_messages', []),
                    'channel_distribution': overview_data.get('channel_distribution', [])
                }
            })
        else:
            return jsonify({'success': False, 'error': '获取数据失败'}), 500
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/channels')
@require_admin_login
def admin_channels():
    """获取频道管理数据"""
    try:
        channels_data = admin_system.get_channels_data()
        return jsonify({
            'success': True,
            'data': channels_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/identities')
@require_admin_login
def admin_identities():
    """获取身份库数据"""
    try:
        identities_data = admin_system.manage_identity_tags('list')
        return jsonify({
            'success': True,
            'data': identities_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/surveys')
@require_admin_login
def admin_surveys():
    """获取问卷数据"""
    try:
        surveys_data = admin_system.get_surveys_data()
        return jsonify({
            'success': True,
            'data': surveys_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/analytics')
@require_admin_login
def admin_analytics():
    """获取数据分析"""
    try:
        analytics_data = admin_system.get_analytics_data()
        return jsonify({
            'success': True,
            'data': analytics_data
        })
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/export/<data_type>')
@require_admin_login
def admin_export(data_type):
    """导出数据"""
    try:
        if data_type == 'chat':
            file_path = admin_system.export_chat_data()
        elif data_type == 'survey':
            file_path = admin_system.export_survey_data()
        elif data_type == 'user':
            file_path = admin_system.export_user_data()
        else:
            return jsonify({'success': False, 'error': '无效的导出类型'}), 400

        return jsonify({'success': True, 'file_path': file_path, 'message': '导出成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/admin/backup', methods=['POST'])
@require_admin_login
def admin_backup():
    """创建备份"""
    try:
        backup_path = security_manager.create_backup()
        return jsonify({'success': True, 'backup_path': backup_path, 'message': '备份创建成功'})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

if __name__ == '__main__':
    # 初始化数据库
    db_manager.init_database()
    
    # 启动应用
    host = config.get('DEFAULT', 'HOST')
    port = config.getint('DEFAULT', 'PORT')
    debug = config.getboolean('DEFAULT', 'DEBUG')
    
    print(f"多身份聊天室系统启动中...")
    print(f"访问地址: http://{host}:{port}")
    
    socketio.run(app, host=host, port=port, debug=debug)

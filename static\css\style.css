/* 多身份聊天室系统 - 自定义样式 */

/* 全局样式 */
body {
    font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

.h-100 {
    height: calc(100vh - 120px) !important;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-radius: 8px;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

/* 消息样式 */
.message-item {
    margin-bottom: 15px;
    padding: 10px;
    border-radius: 8px;
    background-color: #fff;
    border-left: 3px solid #007bff;
}

.message-item.own-message {
    border-left-color: #28a745;
    background-color: #f8fff9;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.message-identities {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.identity-badge {
    font-size: 0.75em;
    padding: 2px 6px;
    border-radius: 12px;
}

.identity-profession { background-color: #007bff; color: white; }
.identity-personality { background-color: #28a745; color: white; }
.identity-emotion { background-color: #ffc107; color: black; }
.identity-hobby { background-color: #dc3545; color: white; }

.message-content {
    margin: 8px 0;
    line-height: 1.4;
}

.message-image {
    max-width: 200px;
    max-height: 200px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s;
}

.message-image:hover {
    transform: scale(1.05);
}

.message-time {
    font-size: 0.8em;
    color: #6c757d;
}

/* 在线成员样式 */
.member-item {
    display: flex;
    align-items: center;
    padding: 5px 0;
    border-bottom: 1px solid #f0f0f0;
}

.member-item:last-child {
    border-bottom: none;
}

.online-indicator {
    width: 8px;
    height: 8px;
    background-color: #28a745;
    border-radius: 50%;
    margin-right: 8px;
}

.offline-indicator {
    width: 8px;
    height: 8px;
    background-color: #6c757d;
    border-radius: 50%;
    margin-right: 8px;
}

/* 频道列表样式 */
.channel-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 10px;
    margin-bottom: 5px;
    border-radius: 6px;
    background-color: #fff;
    border: 1px solid #e9ecef;
    cursor: pointer;
    transition: all 0.2s;
}

.channel-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff;
}

.channel-info {
    flex-grow: 1;
}

.channel-name {
    font-weight: 600;
    margin-bottom: 2px;
}

.channel-meta {
    font-size: 0.8em;
    color: #6c757d;
}

.channel-type-icon {
    margin-left: 10px;
}

/* 身份管理样式 */
.identity-item {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 6px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
}

.identity-item.revealed {
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.identity-name {
    font-weight: 500;
}

.identity-category {
    font-size: 0.8em;
    color: #6c757d;
}

/* 问卷样式 */
.survey-item {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fff;
}

.survey-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 10px;
}

.survey-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.survey-meta {
    font-size: 0.8em;
    color: #6c757d;
}

.question-item {
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    background-color: #f8f9fa;
}

.question-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 8px;
}

.question-type-badge {
    font-size: 0.7em;
    padding: 2px 6px;
    border-radius: 10px;
}

/* 管理后台样式 */
.admin-section {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.stat-card {
    text-align: center;
    padding: 20px;
    border-radius: 8px;
    color: white;
    margin-bottom: 15px;
}

.stat-card h3 {
    font-size: 2em;
    margin-bottom: 5px;
}

.stat-card p {
    margin: 0;
    opacity: 0.9;
}

.stat-primary { background: linear-gradient(135deg, #007bff, #0056b3); }
.stat-success { background: linear-gradient(135deg, #28a745, #1e7e34); }
.stat-info { background: linear-gradient(135deg, #17a2b8, #117a8b); }
.stat-warning { background: linear-gradient(135deg, #ffc107, #e0a800); }
.stat-danger { background: linear-gradient(135deg, #dc3545, #c82333); }

/* 表格样式 */
.table th {
    border-top: none;
    font-weight: 600;
    background-color: #f8f9fa;
}

.table-actions {
    white-space: nowrap;
}

.table-actions .btn {
    margin-right: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .h-100 {
        height: auto !important;
    }
    
    .message-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .message-identities {
        margin-top: 5px;
    }
    
    .channel-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .channel-type-icon {
        margin-left: 0;
        margin-top: 5px;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-20px); opacity: 0; }
    to { transform: translateX(0); opacity: 1; }
}

/* 加载动画 */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 工具提示样式 */
.tooltip-inner {
    max-width: 200px;
    text-align: left;
}

/* 徽章样式 */
.badge-custom {
    font-size: 0.75em;
    padding: 4px 8px;
    border-radius: 12px;
}

/* 按钮组样式 */
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

/* 表单样式增强 */
.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 模态框样式 */
.modal-content {
    border: none;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* 图片预览样式 */
.image-preview {
    position: relative;
    display: inline-block;
}

.image-preview .btn {
    position: absolute;
    top: 5px;
    right: 5px;
    padding: 2px 6px;
    font-size: 0.8em;
}

/* 状态指示器 */
.status-active {
    color: #28a745;
}

.status-inactive {
    color: #6c757d;
}

.status-warning {
    color: #ffc107;
}

.status-danger {
    color: #dc3545;
}

/* 消息容器滚动 */
#messagesContainer {
    overflow-y: auto;
    scroll-behavior: smooth;
}

/* 输入框样式 */
#messageInput {
    border-radius: 20px;
}

/* 文件上传按钮 */
.file-upload-btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

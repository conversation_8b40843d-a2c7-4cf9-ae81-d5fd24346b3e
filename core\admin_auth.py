#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员认证模块
"""

import bcrypt
import uuid
from datetime import datetime, timedelta
from functools import wraps
from flask import session, request, jsonify, redirect, url_for

class AdminAuth:
    """管理员认证系统"""
    
    def __init__(self, db_manager):
        self.db = db_manager
    
    def verify_password(self, username, password):
        """验证管理员密码"""
        try:
            admin = self.db.execute_query('''
                SELECT id, username, password_hash, is_active
                FROM admins 
                WHERE username = ? AND is_active = 1
            ''', (username,))
            
            if not admin:
                return False, "用户名不存在或已禁用"
            
            admin = admin[0]
            stored_hash = admin['password_hash'].encode('utf-8')
            
            if bcrypt.checkpw(password.encode('utf-8'), stored_hash):
                # 更新最后登录时间
                self.db.execute_query('''
                    UPDATE admins SET last_login = CURRENT_TIMESTAMP 
                    WHERE id = ?
                ''', (admin['id'],))
                
                return True, admin
            else:
                return False, "密码错误"
                
        except Exception as e:
            return False, f"验证失败: {e}"
    
    def create_session(self, admin_id, remember_me=False):
        """创建管理员会话"""
        try:
            session_id = str(uuid.uuid4())
            session_token = str(uuid.uuid4())

            # 根据记住登录选项设置过期时间
            if remember_me:
                expires_at = datetime.now() + timedelta(days=30)  # 30天有效期
            else:
                expires_at = datetime.now() + timedelta(hours=24)  # 24小时有效期

            # 清理过期会话
            self.cleanup_expired_sessions()

            # 创建新会话
            self.db.execute_query('''
                INSERT INTO admin_sessions (id, admin_id, session_token, expires_at, remember_me)
                VALUES (?, ?, ?, ?, ?)
            ''', (session_id, admin_id, session_token, expires_at, remember_me))

            return session_token

        except Exception as e:
            print(f"创建会话失败: {e}")
            return None
    
    def verify_session(self, session_token):
        """验证管理员会话"""
        try:
            if not session_token:
                return False, None
            
            session_data = self.db.execute_query('''
                SELECT s.admin_id, s.expires_at, a.username, a.is_active
                FROM admin_sessions s
                JOIN admins a ON s.admin_id = a.id
                WHERE s.session_token = ? AND a.is_active = 1
            ''', (session_token,))
            
            if not session_data:
                return False, "会话不存在"
            
            session_info = session_data[0]
            expires_at = datetime.fromisoformat(session_info['expires_at'])
            
            if expires_at < datetime.now():
                # 删除过期会话
                self.db.execute_query('''
                    DELETE FROM admin_sessions WHERE session_token = ?
                ''', (session_token,))
                return False, "会话已过期"
            
            return True, session_info
            
        except Exception as e:
            print(f"验证会话失败: {e}")
            return False, f"验证失败: {e}"
    
    def logout(self, session_token):
        """管理员登出"""
        try:
            self.db.execute_query('''
                DELETE FROM admin_sessions WHERE session_token = ?
            ''', (session_token,))
            return True
        except Exception as e:
            print(f"登出失败: {e}")
            return False
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        try:
            self.db.execute_query('''
                DELETE FROM admin_sessions WHERE expires_at < CURRENT_TIMESTAMP
            ''')
        except Exception as e:
            print(f"清理过期会话失败: {e}")
    
    def change_password(self, admin_id, old_password, new_password):
        """修改管理员密码"""
        try:
            # 验证旧密码
            admin = self.db.execute_query('''
                SELECT username, password_hash FROM admins WHERE id = ?
            ''', (admin_id,))
            
            if not admin:
                return False, "管理员不存在"
            
            admin = admin[0]
            stored_hash = admin['password_hash'].encode('utf-8')
            
            if not bcrypt.checkpw(old_password.encode('utf-8'), stored_hash):
                return False, "原密码错误"
            
            # 生成新密码哈希
            new_hash = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # 更新密码
            self.db.execute_query('''
                UPDATE admins SET password_hash = ? WHERE id = ?
            ''', (new_hash, admin_id))
            
            return True, "密码修改成功"
            
        except Exception as e:
            return False, f"修改密码失败: {e}"

def require_admin_login(f):
    """管理员登录装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        session_token = session.get('admin_session_token')
        
        if not session_token:
            if request.is_json:
                return jsonify({'success': False, 'error': '需要管理员登录'}), 401
            else:
                return redirect(url_for('admin_login'))
        
        # 这里需要在app.py中设置admin_auth实例
        from app import admin_auth
        is_valid, session_info = admin_auth.verify_session(session_token)
        
        if not is_valid:
            session.pop('admin_session_token', None)
            if request.is_json:
                return jsonify({'success': False, 'error': '会话已过期，请重新登录'}), 401
            else:
                return redirect(url_for('admin_login'))
        
        # 将管理员信息添加到请求上下文
        request.admin_info = session_info
        return f(*args, **kwargs)
    
    return decorated_function

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库结构
"""

from core.database import DatabaseManager
import sqlite3

def check_database_structure():
    """检查数据库结构"""
    db = DatabaseManager('data/chat_system.db')
    conn = db.get_connection()
    cursor = conn.cursor()
    
    print("数据库表结构检查")
    print("=" * 50)
    
    # 获取所有表
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
    tables = cursor.fetchall()
    
    print("数据库中的表:")
    for table in tables:
        table_name = table[0]
        print(f"- {table_name}")
        
        # 获取表结构
        cursor.execute(f"PRAGMA table_info({table_name})")
        columns = cursor.fetchall()
        
        print("  列信息:")
        for col in columns:
            print(f"    {col[1]} ({col[2]})")
        print()
    
    conn.close()

if __name__ == "__main__":
    check_database_structure()

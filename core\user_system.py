#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户身份系统模块
"""

import json
from datetime import datetime
import uuid

class UserSystem:
    """用户身份系统"""
    
    def __init__(self, db_manager):
        self.db = db_manager
    
    def create_user(self, user_id):
        """创建新用户"""
        try:
            self.db.execute_query(
                "INSERT OR IGNORE INTO users (id) VALUES (?)",
                (user_id,)
            )
            return True
        except Exception as e:
            print(f"创建用户失败: {e}")
            return False
    
    def get_user_info(self, user_id):
        """获取用户信息"""
        result = self.db.execute_query(
            "SELECT * FROM users WHERE id = ?",
            (user_id,)
        )
        return dict(result[0]) if result else None
    
    def update_user_activity(self, user_id):
        """更新用户活跃时间"""
        self.db.execute_query(
            "UPDATE users SET last_active = CURRENT_TIMESTAMP WHERE id = ?",
            (user_id,)
        )
    
    def get_available_identity_tags(self, category=None):
        """获取可用的身份标签"""
        if category:
            query = "SELECT * FROM identity_tags WHERE category = ? ORDER BY name"
            params = (category,)
        else:
            query = "SELECT * FROM identity_tags ORDER BY category, name"
            params = None
        
        results = self.db.execute_query(query, params)
        return [dict(row) for row in results]
    
    def get_user_identities(self, user_id):
        """获取用户的身份列表"""
        results = self.db.execute_query('''
            SELECT ui.*, it.description 
            FROM user_identities ui
            LEFT JOIN identity_tags it ON ui.identity_name = it.name AND ui.category = it.category
            WHERE ui.user_id = ? AND ui.is_active = 1
            ORDER BY ui.category, ui.identity_name
        ''', (user_id,))
        
        identities = {}
        for row in results:
            category = row['category']
            if category not in identities:
                identities[category] = []
            identities[category].append({
                'id': row['id'],
                'name': row['identity_name'],
                'description': row['description'],
                'created_at': row['created_at']
            })
        
        return identities
    
    def add_user_identity(self, user_id, identity_name, category):
        """为用户添加身份"""
        # 检查用户是否已有3个身份
        count_result = self.db.execute_query('''
            SELECT COUNT(*) as count FROM user_identities 
            WHERE user_id = ? AND is_active = 1
        ''', (user_id,))
        
        if count_result and count_result[0]['count'] >= 3:
            return False, "每个用户最多只能选择3个身份"
        
        # 检查身份是否已存在
        existing = self.db.execute_query('''
            SELECT id FROM user_identities 
            WHERE user_id = ? AND identity_name = ? AND category = ?
        ''', (user_id, identity_name, category))
        
        if existing:
            return False, "该身份已存在"
        
        # 验证身份标签是否存在
        tag_exists = self.db.execute_query('''
            SELECT id FROM identity_tags 
            WHERE name = ? AND category = ?
        ''', (identity_name, category))
        
        if not tag_exists:
            return False, "身份标签不存在"
        
        try:
            self.db.execute_query('''
                INSERT INTO user_identities (user_id, identity_name, category)
                VALUES (?, ?, ?)
            ''', (user_id, identity_name, category))
            return True, "身份添加成功"
        except Exception as e:
            return False, f"添加身份失败: {e}"
    
    def remove_user_identity(self, user_id, identity_id):
        """移除用户身份"""
        try:
            affected = self.db.execute_query('''
                UPDATE user_identities 
                SET is_active = 0 
                WHERE id = ? AND user_id = ?
            ''', (identity_id, user_id))
            
            if affected > 0:
                return True, "身份移除成功"
            else:
                return False, "身份不存在或无权限"
        except Exception as e:
            return False, f"移除身份失败: {e}"
    
    def get_user_channel_identities(self, user_id, channel_id):
        """获取用户在特定频道已公开的身份"""
        results = self.db.execute_query('''
            SELECT identity_name FROM user_channel_identities
            WHERE user_id = ? AND channel_id = ?
            ORDER BY revealed_at
        ''', (user_id, channel_id))
        
        return [row['identity_name'] for row in results]
    
    def reveal_identity_in_channel(self, user_id, channel_id, identity_name):
        """在频道中公开身份"""
        # 检查用户是否拥有该身份
        user_identity = self.db.execute_query('''
            SELECT id FROM user_identities 
            WHERE user_id = ? AND identity_name = ? AND is_active = 1
        ''', (user_id, identity_name))
        
        if not user_identity:
            return False, "用户不拥有该身份"
        
        # 检查是否已经公开
        existing = self.db.execute_query('''
            SELECT id FROM user_channel_identities
            WHERE user_id = ? AND channel_id = ? AND identity_name = ?
        ''', (user_id, channel_id, identity_name))
        
        if existing:
            return False, "该身份已在此频道公开"
        
        try:
            self.db.execute_query('''
                INSERT INTO user_channel_identities (user_id, channel_id, identity_name)
                VALUES (?, ?, ?)
            ''', (user_id, channel_id, identity_name))
            return True, "身份公开成功"
        except Exception as e:
            return False, f"公开身份失败: {e}"
    
    def hide_identity_in_channel(self, user_id, channel_id, identity_name):
        """在频道中隐藏身份"""
        try:
            affected = self.db.execute_query('''
                DELETE FROM user_channel_identities
                WHERE user_id = ? AND channel_id = ? AND identity_name = ?
            ''', (user_id, channel_id, identity_name))
            
            if affected > 0:
                return True, "身份隐藏成功"
            else:
                return False, "身份未公开或不存在"
        except Exception as e:
            return False, f"隐藏身份失败: {e}"
    
    def get_identity_statistics(self):
        """获取身份使用统计"""
        results = self.db.execute_query('''
            SELECT 
                ui.category,
                ui.identity_name,
                COUNT(*) as user_count,
                it.description
            FROM user_identities ui
            LEFT JOIN identity_tags it ON ui.identity_name = it.name AND ui.category = it.category
            WHERE ui.is_active = 1
            GROUP BY ui.category, ui.identity_name
            ORDER BY ui.category, user_count DESC
        ''')
        
        stats = {}
        for row in results:
            category = row['category']
            if category not in stats:
                stats[category] = []
            stats[category].append({
                'name': row['identity_name'],
                'description': row['description'],
                'user_count': row['user_count']
            })
        
        return stats
    
    def search_users_by_identity(self, identity_name, category=None):
        """根据身份搜索用户"""
        if category:
            query = '''
                SELECT DISTINCT ui.user_id, u.created_at, u.last_active
                FROM user_identities ui
                JOIN users u ON ui.user_id = u.id
                WHERE ui.identity_name = ? AND ui.category = ? AND ui.is_active = 1
                ORDER BY u.last_active DESC
            '''
            params = (identity_name, category)
        else:
            query = '''
                SELECT DISTINCT ui.user_id, u.created_at, u.last_active
                FROM user_identities ui
                JOIN users u ON ui.user_id = u.id
                WHERE ui.identity_name = ? AND ui.is_active = 1
                ORDER BY u.last_active DESC
            '''
            params = (identity_name,)
        
        results = self.db.execute_query(query, params)
        return [dict(row) for row in results]

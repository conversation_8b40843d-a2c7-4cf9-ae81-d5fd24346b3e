@echo off
chcp 65001 >nul
echo ========================================
echo 多身份聊天室系统 - Windows部署脚本
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [信息] 检测到Python版本:
python --version

:: 检查pip是否可用
pip --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] pip不可用，请检查Python安装
    pause
    exit /b 1
)

echo [信息] 检测到pip版本:
pip --version
echo.

:: 创建虚拟环境
echo [步骤1] 创建Python虚拟环境...
if not exist "venv" (
    python -m venv venv
    if %errorlevel% neq 0 (
        echo [错误] 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo [成功] 虚拟环境创建完成
) else (
    echo [信息] 虚拟环境已存在
)

:: 激活虚拟环境
echo [步骤2] 激活虚拟环境...
call venv\Scripts\activate.bat
if %errorlevel% neq 0 (
    echo [错误] 激活虚拟环境失败
    pause
    exit /b 1
)
echo [成功] 虚拟环境已激活

:: 升级pip
echo [步骤3] 升级pip...
python -m pip install --upgrade pip
echo.

:: 安装依赖
echo [步骤4] 安装项目依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [错误] 安装依赖失败，请检查requirements.txt文件
    pause
    exit /b 1
)
echo [成功] 依赖安装完成
echo.

:: 创建必要的目录
echo [步骤5] 创建必要的目录结构...
if not exist "uploads" mkdir uploads
if not exist "uploads\images" mkdir uploads\images
if not exist "uploads\thumbnails" mkdir uploads\thumbnails
if not exist "backups" mkdir backups
if not exist "logs" mkdir logs
echo [成功] 目录结构创建完成

:: 初始化数据库
echo [步骤6] 初始化数据库...
python -c "from core.database import DatabaseManager; db = DatabaseManager('config.ini'); db.initialize_database(); print('数据库初始化完成')"
if %errorlevel% neq 0 (
    echo [错误] 数据库初始化失败
    pause
    exit /b 1
)
echo [成功] 数据库初始化完成
echo.

:: 检查配置文件
echo [步骤7] 检查配置文件...
if not exist "config.ini" (
    echo [警告] config.ini文件不存在，将使用默认配置
) else (
    echo [信息] 配置文件存在
)

:: 创建启动脚本
echo [步骤8] 创建启动脚本...
echo @echo off > start_server.bat
echo chcp 65001 ^>nul >> start_server.bat
echo echo 启动多身份聊天室系统... >> start_server.bat
echo call venv\Scripts\activate.bat >> start_server.bat
echo python app.py >> start_server.bat
echo pause >> start_server.bat

echo @echo off > start_server_debug.bat
echo chcp 65001 ^>nul >> start_server_debug.bat
echo echo 启动多身份聊天室系统（调试模式）... >> start_server_debug.bat
echo call venv\Scripts\activate.bat >> start_server_debug.bat
echo set FLASK_ENV=development >> start_server_debug.bat
echo set FLASK_DEBUG=1 >> start_server_debug.bat
echo python app.py >> start_server_debug.bat
echo pause >> start_server_debug.bat

echo [成功] 启动脚本创建完成

:: 创建停止脚本
echo [步骤9] 创建停止脚本...
echo @echo off > stop_server.bat
echo echo 正在停止服务器... >> stop_server.bat
echo taskkill /f /im python.exe 2^>nul >> stop_server.bat
echo echo 服务器已停止 >> stop_server.bat
echo pause >> stop_server.bat

echo [成功] 停止脚本创建完成

:: 设置防火墙规则（可选）
echo [步骤10] 配置防火墙规则...
netsh advfirewall firewall show rule name="多身份聊天室" >nul 2>&1
if %errorlevel% neq 0 (
    echo [信息] 添加防火墙规则（需要管理员权限）...
    netsh advfirewall firewall add rule name="多身份聊天室" dir=in action=allow protocol=TCP localport=5000 >nul 2>&1
    if %errorlevel% equ 0 (
        echo [成功] 防火墙规则添加成功
    ) else (
        echo [警告] 防火墙规则添加失败，可能需要管理员权限
    )
) else (
    echo [信息] 防火墙规则已存在
)

echo.
echo ========================================
echo 部署完成！
echo ========================================
echo.
echo 使用说明:
echo 1. 运行 start_server.bat 启动服务器
echo 2. 运行 start_server_debug.bat 启动调试模式
echo 3. 运行 stop_server.bat 停止服务器
echo 4. 在浏览器中访问 http://localhost:5000
echo.
echo 注意事项:
echo - 确保端口5000未被占用
echo - 首次运行会自动创建管理员账户
echo - 数据库文件位于 data/chat_system.db
echo - 上传文件保存在 uploads/ 目录
echo - 备份文件保存在 backups/ 目录
echo.

:: 询问是否立即启动
set /p choice="是否立即启动服务器？(y/n): "
if /i "%choice%"=="y" (
    echo.
    echo 正在启动服务器...
    call start_server.bat
) else (
    echo.
    echo 部署完成，您可以稍后运行 start_server.bat 启动服务器
)

pause

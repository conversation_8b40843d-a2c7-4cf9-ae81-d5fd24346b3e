#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试管理员登录功能
"""

import requests
import json

def test_admin_login():
    """测试管理员登录功能"""
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    print("测试管理员登录功能...")
    print("=" * 50)
    
    # 1. 测试未登录访问管理后台
    print("1. 测试未登录访问管理后台...")
    try:
        response = session.get(f"{base_url}/admin", allow_redirects=False)
        print(f"状态码: {response.status_code}")
        if response.status_code == 302:
            print("✓ 未登录访问管理后台被正确重定向")
        else:
            print("✗ 未登录访问管理后台没有被重定向")
    except Exception as e:
        print(f"✗ 测试异常: {e}")
    
    print()
    
    # 2. 测试管理员登录
    print("2. 测试管理员登录...")
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        response = session.post(f"{base_url}/admin/login", data=login_data, allow_redirects=False)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 302:
            print("✓ 管理员登录成功，被重定向到管理后台")
            
            # 3. 测试登录后访问管理后台
            print("\n3. 测试登录后访问管理后台...")
            response = session.get(f"{base_url}/admin")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✓ 登录后可以正常访问管理后台")
                
                # 4. 测试管理后台API
                print("\n4. 测试管理后台API...")
                response = session.get(f"{base_url}/api/admin/overview")
                print(f"API状态码: {response.status_code}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('success'):
                        print("✓ 登录后可以正常访问管理后台API")
                        print(f"系统概览数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    else:
                        print("✗ 管理后台API返回失败")
                else:
                    print("✗ 管理后台API访问失败")
            else:
                print("✗ 登录后无法访问管理后台")
        else:
            print("✗ 管理员登录失败")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"✗ 测试异常: {e}")
    
    print()
    
    # 5. 测试错误密码登录
    print("5. 测试错误密码登录...")
    try:
        login_data = {
            'username': 'admin',
            'password': 'wrongpassword'
        }
        response = session.post(f"{base_url}/admin/login", data=login_data)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200 and "密码错误" in response.text:
            print("✓ 错误密码登录被正确拒绝")
        else:
            print("✗ 错误密码登录处理异常")
    except Exception as e:
        print(f"✗ 测试异常: {e}")
    
    print()
    print("=" * 50)
    print("管理员登录功能测试完成")

if __name__ == "__main__":
    test_admin_login()

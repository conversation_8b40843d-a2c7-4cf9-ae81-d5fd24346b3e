#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
频道管理系统模块
"""

import uuid
import bcrypt
import sqlite3
from datetime import datetime, timedelta
import json

class ChannelSystem:
    """频道管理系统"""
    
    def __init__(self, db_manager):
        self.db = db_manager
    
    def create_channel(self, creator_id, name, channel_type, password=None, max_members=0):
        """创建频道"""
        # 详细的输入验证
        if not name or not name.strip():
            return False, None, "频道名称不能为空"

        name = name.strip()
        if len(name) > 50:
            return False, None, "频道名称不能超过50个字符"

        if len(name) < 2:
            return False, None, "频道名称至少需要2个字符"

        # 检查频道名称是否已存在
        existing_channel = self.db.fetch_one('''
            SELECT id FROM channels WHERE name = ? AND is_active = 1
        ''', (name,))

        if existing_channel:
            return False, None, "频道名称已存在，请选择其他名称"

        # 验证频道类型
        valid_types = ['public', 'password', 'invite']
        if channel_type not in valid_types:
            return False, None, f"无效的频道类型，支持的类型: {', '.join(valid_types)}"

        # 验证密码
        if channel_type == 'password':
            if not password:
                return False, None, "密码频道必须设置密码"
            if len(password) < 4:
                return False, None, "密码长度至少需要4个字符"
            if len(password) > 50:
                return False, None, "密码长度不能超过50个字符"

        # 验证成员上限
        if max_members < 0:
            return False, None, "成员上限不能为负数"
        if max_members > 1000:
            return False, None, "成员上限不能超过1000人"

        # 检查用户是否已创建过多频道
        user_channel_count = self.db.fetch_one('''
            SELECT COUNT(*) as count FROM channels
            WHERE creator_id = ? AND is_active = 1
        ''', (creator_id,))

        if user_channel_count and user_channel_count[0] >= 10:
            return False, None, "每个用户最多只能创建10个频道"

        channel_id = str(uuid.uuid4())
        password_hash = None

        # 如果是密码频道，加密密码
        if channel_type == 'password' and password:
            try:
                password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            except Exception as e:
                return False, None, f"密码加密失败: {str(e)}"

        try:
            # 创建频道
            self.db.execute_query('''
                INSERT INTO channels (id, name, type, password_hash, max_members, creator_id)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (channel_id, name, channel_type, password_hash, max_members, creator_id))

            # 创建者自动加入频道
            join_success, join_message = self.join_channel(creator_id, channel_id)
            if not join_success:
                # 如果加入失败，删除刚创建的频道
                self.db.execute_query('DELETE FROM channels WHERE id = ?', (channel_id,))
                return False, None, f"频道创建成功但加入失败: {join_message}"

            return True, channel_id, "频道创建成功"

        except sqlite3.IntegrityError as e:
            if 'UNIQUE constraint failed' in str(e):
                return False, None, "频道名称已存在，请选择其他名称"
            else:
                return False, None, f"数据库约束错误: {str(e)}"
        except sqlite3.Error as e:
            return False, None, f"数据库操作失败: {str(e)}"
        except Exception as e:
            return False, None, f"创建频道时发生未知错误: {str(e)}"
    
    def get_channel_info(self, channel_id):
        """获取频道信息"""
        result = self.db.execute_query('''
            SELECT c.*, u.id as creator_name,
                   (SELECT COUNT(*) FROM channel_members WHERE channel_id = c.id) as member_count
            FROM channels c
            LEFT JOIN users u ON c.creator_id = u.id
            WHERE c.id = ? AND c.is_active = 1
        ''', (channel_id,))
        
        if result:
            channel_info = dict(result[0])
            # 不返回密码哈希
            if 'password_hash' in channel_info:
                del channel_info['password_hash']
            return channel_info
        return None
    
    def get_public_channels(self, limit=50):
        """获取公开频道列表"""
        results = self.db.execute_query('''
            SELECT c.id, c.name, c.max_members, c.created_at,
                   (SELECT COUNT(*) FROM channel_members WHERE channel_id = c.id) as member_count
            FROM channels c
            WHERE c.type = 'public' AND c.is_active = 1
            ORDER BY c.created_at DESC
            LIMIT ?
        ''', (limit,))
        
        return [dict(row) for row in results]
    
    def join_channel(self, user_id, channel_id, password=None):
        """加入频道"""
        # 输入验证
        if not user_id or not user_id.strip():
            return False, "用户ID无效"

        if not channel_id or not channel_id.strip():
            return False, "频道ID无效"

        # 获取频道信息
        channel = self.db.fetch_one('''
            SELECT * FROM channels WHERE id = ? AND is_active = 1
        ''', (channel_id,))

        if not channel:
            return False, "频道不存在或已被删除"

        channel = dict(channel)

        # 检查是否已经是成员
        existing_member = self.db.fetch_one('''
            SELECT id FROM channel_members WHERE channel_id = ? AND user_id = ?
        ''', (channel_id, user_id))

        if existing_member:
            return False, "您已经是该频道的成员"

        # 检查人数限制
        if channel['max_members'] > 0:
            member_count = self.db.fetch_one('''
                SELECT COUNT(*) as count FROM channel_members WHERE channel_id = ?
            ''', (channel_id,))

            current_count = member_count[0] if member_count else 0
            if current_count >= channel['max_members']:
                return False, f"频道人数已满 ({current_count}/{channel['max_members']})"

        # 验证权限
        if channel['type'] == 'password':
            if not password:
                return False, "该频道需要密码才能加入"

            if not password.strip():
                return False, "密码不能为空"

            try:
                if not bcrypt.checkpw(password.encode('utf-8'), channel['password_hash'].encode('utf-8')):
                    return False, "密码错误，请检查后重试"
            except Exception as e:
                return False, f"密码验证失败: {str(e)}"

        elif channel['type'] == 'invite':
            # 邀请制频道需要有效的邀请链接
            return False, "该频道为邀请制，需要有效的邀请链接才能加入"
        
        try:
            # 加入频道
            self.db.execute_query('''
                INSERT INTO channel_members (channel_id, user_id)
                VALUES (?, ?)
            ''', (channel_id, user_id))

            # 用户加入时默认公开第一个身份
            try:
                self._auto_reveal_first_identity(user_id, channel_id)
            except Exception as identity_error:
                # 身份公开失败不影响加入频道
                print(f"自动公开身份失败: {identity_error}")

            return True, f"成功加入频道 '{channel['name']}'"

        except sqlite3.IntegrityError as e:
            if 'UNIQUE constraint failed' in str(e):
                return False, "您已经是该频道的成员"
            else:
                return False, f"数据库约束错误: {str(e)}"
        except sqlite3.Error as e:
            return False, f"数据库操作失败: {str(e)}"
        except Exception as e:
            return False, f"加入频道时发生未知错误: {str(e)}"
    
    def join_channel_by_invite(self, user_id, invite_id):
        """通过邀请链接加入频道"""
        # 验证邀请链接
        invite = self.db.execute_query('''
            SELECT * FROM invite_links 
            WHERE id = ? AND is_active = 1 AND expires_at > CURRENT_TIMESTAMP
        ''', (invite_id,))
        
        if not invite:
            return False, "邀请链接无效或已过期"
        
        invite = dict(invite[0])
        
        # 检查使用次数限制
        if invite['max_uses'] > 0 and invite['used_count'] >= invite['max_uses']:
            return False, "邀请链接使用次数已达上限"
        
        # 加入频道
        success, message = self.join_channel(user_id, invite['channel_id'])
        
        if success:
            # 更新邀请链接使用次数
            self.db.execute_query('''
                UPDATE invite_links SET used_count = used_count + 1 WHERE id = ?
            ''', (invite_id,))
        
        return success, message
    
    def leave_channel(self, user_id, channel_id):
        """离开频道"""
        try:
            affected = self.db.execute_query('''
                DELETE FROM channel_members WHERE channel_id = ? AND user_id = ?
            ''', (channel_id, user_id))
            
            if affected > 0:
                # 清除在该频道的身份公开记录
                self.db.execute_query('''
                    DELETE FROM user_channel_identities 
                    WHERE user_id = ? AND channel_id = ?
                ''', (user_id, channel_id))
                
                return True, "成功离开频道"
            else:
                return False, "不是频道成员"
        except Exception as e:
            return False, f"离开频道失败: {e}"
    
    def can_access_channel(self, user_id, channel_id):
        """检查用户是否可以访问频道"""
        member = self.db.execute_query('''
            SELECT id FROM channel_members WHERE channel_id = ? AND user_id = ?
        ''', (channel_id, user_id))
        
        return bool(member)
    
    def get_channel_members(self, channel_id, include_offline=True):
        """获取频道成员列表"""
        if include_offline:
            query = '''
                SELECT cm.user_id, cm.joined_at, cm.is_online,
                       GROUP_CONCAT(uci.identity_name) as revealed_identities
                FROM channel_members cm
                LEFT JOIN user_channel_identities uci ON cm.user_id = uci.user_id AND cm.channel_id = uci.channel_id
                WHERE cm.channel_id = ?
                GROUP BY cm.user_id, cm.joined_at, cm.is_online
                ORDER BY cm.is_online DESC, cm.joined_at
            '''
        else:
            query = '''
                SELECT cm.user_id, cm.joined_at, cm.is_online,
                       GROUP_CONCAT(uci.identity_name) as revealed_identities
                FROM channel_members cm
                LEFT JOIN user_channel_identities uci ON cm.user_id = uci.user_id AND cm.channel_id = uci.channel_id
                WHERE cm.channel_id = ? AND cm.is_online = 1
                GROUP BY cm.user_id, cm.joined_at, cm.is_online
                ORDER BY cm.joined_at
            '''
        
        results = self.db.execute_query(query, (channel_id,))
        
        members = []
        for row in results:
            member = dict(row)
            # 处理身份列表
            if member['revealed_identities']:
                member['revealed_identities'] = member['revealed_identities'].split(',')
            else:
                member['revealed_identities'] = []
            members.append(member)
        
        return members
    
    def update_member_online_status(self, user_id, channel_id, is_online):
        """更新成员在线状态"""
        self.db.execute_query('''
            UPDATE channel_members 
            SET is_online = ? 
            WHERE user_id = ? AND channel_id = ?
        ''', (is_online, user_id, channel_id))
    
    def create_invite_link(self, creator_id, channel_id, expires_hours=24, max_uses=0):
        """创建邀请链接"""
        # 检查是否有权限创建邀请链接
        channel = self.db.execute_query('''
            SELECT creator_id FROM channels WHERE id = ? AND is_active = 1
        ''', (channel_id,))
        
        if not channel or channel[0]['creator_id'] != creator_id:
            return False, None, "无权限创建邀请链接"
        
        invite_id = str(uuid.uuid4())
        expires_at = datetime.now() + timedelta(hours=expires_hours)
        
        try:
            self.db.execute_query('''
                INSERT INTO invite_links (id, channel_id, created_by, expires_at, max_uses)
                VALUES (?, ?, ?, ?, ?)
            ''', (invite_id, channel_id, creator_id, expires_at, max_uses))
            
            return True, invite_id, "邀请链接创建成功"
        except Exception as e:
            return False, None, f"创建邀请链接失败: {e}"
    
    def delete_channel(self, user_id, channel_id):
        """删除频道（仅创建者可删除）"""
        # 检查权限
        channel = self.db.execute_query('''
            SELECT creator_id FROM channels WHERE id = ? AND is_active = 1
        ''', (channel_id,))
        
        if not channel or channel[0]['creator_id'] != user_id:
            return False, "无权限删除频道"
        
        try:
            # 软删除频道
            self.db.execute_query('''
                UPDATE channels SET is_active = 0 WHERE id = ?
            ''', (channel_id,))
            
            # 清除所有成员
            self.db.execute_query('''
                DELETE FROM channel_members WHERE channel_id = ?
            ''', (channel_id,))
            
            # 清除身份公开记录
            self.db.execute_query('''
                DELETE FROM user_channel_identities WHERE channel_id = ?
            ''', (channel_id,))
            
            # 停用邀请链接
            self.db.execute_query('''
                UPDATE invite_links SET is_active = 0 WHERE channel_id = ?
            ''', (channel_id,))
            
            return True, "频道删除成功"
        except Exception as e:
            return False, f"删除频道失败: {e}"
    
    def _auto_reveal_first_identity(self, user_id, channel_id):
        """自动公开用户的第一个身份"""
        # 获取用户的第一个身份
        first_identity = self.db.execute_query('''
            SELECT identity_name FROM user_identities 
            WHERE user_id = ? AND is_active = 1 
            ORDER BY created_at LIMIT 1
        ''', (user_id,))
        
        if first_identity:
            identity_name = first_identity[0]['identity_name']
            self.db.execute_query('''
                INSERT OR IGNORE INTO user_channel_identities (user_id, channel_id, identity_name)
                VALUES (?, ?, ?)
            ''', (user_id, channel_id, identity_name))
    
    def get_user_channels(self, user_id):
        """获取用户加入的频道列表"""
        results = self.db.execute_query('''
            SELECT c.id, c.name, c.type, c.created_at, cm.joined_at,
                   (SELECT COUNT(*) FROM channel_members WHERE channel_id = c.id) as member_count
            FROM channels c
            JOIN channel_members cm ON c.id = cm.channel_id
            WHERE cm.user_id = ? AND c.is_active = 1
            ORDER BY cm.joined_at DESC
        ''', (user_id,))
        
        return [dict(row) for row in results]

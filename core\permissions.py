#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户权限管理模块
"""

import sqlite3
from datetime import datetime

class PermissionManager:
    """用户权限管理器"""
    
    def __init__(self, db_manager):
        self.db = db_manager
    
    def check_channel_creation_permission(self, user_id):
        """检查用户是否有创建频道的权限"""
        try:
            result = self.db.fetch_one('''
                SELECT can_create_channels FROM users WHERE id = ?
            ''', (user_id,))
            
            if result:
                return bool(result[0])
            else:
                # 用户不存在，创建用户记录（默认无权限）
                self.create_user_if_not_exists(user_id)
                return False
                
        except Exception as e:
            print(f"检查频道创建权限失败: {e}")
            return False
    
    def create_user_if_not_exists(self, user_id):
        """如果用户不存在则创建用户记录"""
        try:
            # 检查用户是否存在
            existing = self.db.fetch_one('SELECT id FROM users WHERE id = ?', (user_id,))
            
            if not existing:
                # 创建新用户，默认无创建频道权限
                self.db.execute_query('''
                    INSERT INTO users (id, can_create_channels, created_at, last_active)
                    VALUES (?, 0, ?, ?)
                ''', (user_id, datetime.now(), datetime.now()))
                
        except Exception as e:
            print(f"创建用户记录失败: {e}")
    
    def grant_channel_creation_permission(self, user_id):
        """授予用户创建频道的权限"""
        try:
            # 确保用户存在
            self.create_user_if_not_exists(user_id)
            
            # 授予权限
            self.db.execute_query('''
                UPDATE users SET can_create_channels = 1 WHERE id = ?
            ''', (user_id,))
            
            return True
            
        except Exception as e:
            print(f"授予频道创建权限失败: {e}")
            return False
    
    def revoke_channel_creation_permission(self, user_id):
        """撤销用户创建频道的权限"""
        try:
            self.db.execute_query('''
                UPDATE users SET can_create_channels = 0 WHERE id = ?
            ''', (user_id,))
            
            return True
            
        except Exception as e:
            print(f"撤销频道创建权限失败: {e}")
            return False
    
    def get_users_with_permissions(self):
        """获取所有有权限的用户列表"""
        try:
            results = self.db.fetch_all('''
                SELECT u.id, u.can_create_channels, u.created_at, u.last_active,
                       COUNT(c.id) as created_channels_count
                FROM users u
                LEFT JOIN channels c ON c.creator_id = u.id
                WHERE u.can_create_channels = 1
                GROUP BY u.id
                ORDER BY u.created_at DESC
            ''')
            
            users = []
            for row in results:
                users.append({
                    'id': row[0],
                    'can_create_channels': bool(row[1]),
                    'created_at': row[2],
                    'last_active': row[3],
                    'created_channels_count': row[4]
                })
            
            return users
            
        except Exception as e:
            print(f"获取权限用户列表失败: {e}")
            return []
    
    def get_all_users_with_stats(self):
        """获取所有用户及其统计信息"""
        try:
            results = self.db.fetch_all('''
                SELECT u.id, u.can_create_channels, u.created_at, u.last_active,
                       COUNT(DISTINCT c.id) as created_channels_count,
                       COUNT(DISTINCT m.id) as messages_count,
                       COUNT(DISTINCT cm.channel_id) as joined_channels_count
                FROM users u
                LEFT JOIN channels c ON c.creator_id = u.id
                LEFT JOIN messages m ON m.user_id = u.id
                LEFT JOIN channel_members cm ON cm.user_id = u.id
                GROUP BY u.id
                ORDER BY u.last_active DESC
            ''')
            
            users = []
            for row in results:
                users.append({
                    'id': row[0],
                    'can_create_channels': bool(row[1]),
                    'created_at': row[2],
                    'last_active': row[3],
                    'created_channels_count': row[4],
                    'messages_count': row[5],
                    'joined_channels_count': row[6]
                })
            
            return users
            
        except Exception as e:
            print(f"获取用户统计信息失败: {e}")
            return []
    
    def update_user_last_active(self, user_id):
        """更新用户最后活跃时间"""
        try:
            # 确保用户存在
            self.create_user_if_not_exists(user_id)
            
            # 更新最后活跃时间
            self.db.execute_query('''
                UPDATE users SET last_active = ? WHERE id = ?
            ''', (datetime.now(), user_id))
            
        except Exception as e:
            print(f"更新用户活跃时间失败: {e}")

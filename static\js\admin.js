// 管理后台JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadSystemOverview();
    loadChannelsList();
    loadIdentityTags();
    initializeEventListeners();
});

// 显示管理后台不同部分
function showAdminSection(section, element) {
    // 移除所有活动状态
    document.querySelectorAll('.list-group-item-action').forEach(item => {
        item.classList.remove('active');
    });

    // 设置当前项为活动状态
    if (element) {
        element.classList.add('active');
    }

    // 隐藏所有内容区域
    document.querySelectorAll('.admin-section').forEach(section => {
        section.style.display = 'none';
    });

    // 显示对应的内容区域
    const targetSection = document.getElementById(section + '-section');
    if (targetSection) {
        targetSection.style.display = 'block';
    }

    // 根据不同部分加载对应数据
    switch (section) {
        case 'overview':
            loadSystemOverview();
            break;
        case 'channels':
            loadChannelsList();
            break;
        case 'identities':
            loadIdentityTags();
            break;
        case 'surveys':
            loadSurveysList();
            break;
        case 'analytics':
            loadAnalyticsData();
            break;
        case 'export':
            // 导出页面不需要额外加载数据
            break;
        case 'security':
            loadSecuritySettings();
            break;
        case 'backup':
            loadBackupList();
            break;
    }

    // 阻止默认链接行为
    return false;
}

// 初始化事件监听器
function initializeEventListeners() {
    // 标签页切换事件
    const tabButtons = document.querySelectorAll('[data-bs-toggle="tab"]');
    tabButtons.forEach(button => {
        button.addEventListener('shown.bs.tab', function(e) {
            const target = e.target.getAttribute('data-bs-target');
            handleTabSwitch(target);
        });
    });
    
    // 添加身份标签表单
    const addTagForm = document.getElementById('addTagForm');
    if (addTagForm) {
        addTagForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addIdentityTag();
        });
    }
    
    // 数据导出表单
    const exportForm = document.getElementById('exportForm');
    if (exportForm) {
        exportForm.addEventListener('submit', function(e) {
            e.preventDefault();
            exportData();
        });
    }
}

// 处理标签页切换
function handleTabSwitch(target) {
    switch (target) {
        case '#overview':
            loadSystemOverview();
            break;
        case '#channels':
            loadChannelsList();
            break;
        case '#identities':
            loadIdentityTags();
            break;
        case '#data':
            // 数据导出页面不需要额外加载
            break;
        case '#security':
            loadSecurityInfo();
            break;
    }
}

// 加载系统概览
function loadSystemOverview() {
    fetch('/api/admin/overview')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateOverviewStats(data.stats);
                renderCharts(data.charts);
            }
        })
        .catch(error => {
            console.error('加载系统概览失败:', error);
        });
}

// 更新概览统计
function updateOverviewStats(stats) {
    const elements = {
        'totalUsers': stats.total_users || 0,
        'totalChannels': stats.total_channels || 0,
        'totalMessages': stats.total_messages || 0,
        'activeSurveys': stats.active_surveys || 0,
        'onlineUsers': stats.online_users || 0,
        'todayMessages': stats.today_messages || 0
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
}

// 渲染图表
function renderCharts(chartData) {
    // 消息趋势图
    if (chartData.message_trend && document.getElementById('messageTrendChart')) {
        renderMessageTrendChart(chartData.message_trend);
    }
    
    // 频道类型分布图
    if (chartData.channel_distribution && document.getElementById('channelDistributionChart')) {
        renderChannelDistributionChart(chartData.channel_distribution);
    }
    
    // 身份使用统计图
    if (chartData.identity_usage && document.getElementById('identityUsageChart')) {
        renderIdentityUsageChart(chartData.identity_usage);
    }
}

// 渲染消息趋势图
function renderMessageTrendChart(data) {
    const ctx = document.getElementById('messageTrendChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.labels,
            datasets: [{
                label: '消息数量',
                data: data.values,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '最近7天消息趋势'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 渲染频道分布图
function renderChannelDistributionChart(data) {
    const ctx = document.getElementById('channelDistributionChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels,
            datasets: [{
                data: data.values,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '频道类型分布'
                }
            }
        }
    });
}

// 渲染身份使用统计图
function renderIdentityUsageChart(data) {
    const ctx = document.getElementById('identityUsageChart').getContext('2d');
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: data.labels,
            datasets: [{
                label: '使用次数',
                data: data.values,
                backgroundColor: 'rgba(153, 102, 255, 0.8)'
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: true,
                    text: '热门身份标签'
                }
            },
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

// 加载频道列表
function loadChannelsList() {
    fetch('/api/admin/channels')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateChannelsList(data.channels);
            }
        })
        .catch(error => {
            console.error('加载频道列表失败:', error);
        });
}

// 更新频道列表
function updateChannelsList(channels) {
    const tbody = document.querySelector('#channelsTable tbody');
    if (!tbody) return;
    
    tbody.innerHTML = '';
    
    channels.forEach(channel => {
        const row = document.createElement('tr');
        
        let typeText = '';
        let typeClass = '';
        switch (channel.type) {
            case 'public':
                typeText = '公开';
                typeClass = 'badge bg-success';
                break;
            case 'password':
                typeText = '密码';
                typeClass = 'badge bg-warning';
                break;
            case 'invite':
                typeText = '邀请';
                typeClass = 'badge bg-info';
                break;
        }
        
        row.innerHTML = `
            <td>${channel.name}</td>
            <td><span class="${typeClass}">${typeText}</span></td>
            <td>${channel.member_count}</td>
            <td>${channel.message_count}</td>
            <td>${formatTime(channel.created_at)}</td>
            <td>
                <button class="btn btn-sm btn-primary" onclick="viewChannel('${channel.id}')">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-danger" onclick="deleteChannel('${channel.id}')">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// 查看频道详情
function viewChannel(channelId) {
    window.open(`/chat/${channelId}`, '_blank');
}

// 删除频道
function deleteChannel(channelId) {
    if (!confirm('确定要删除这个频道吗？此操作不可恢复。')) {
        return;
    }
    
    fetch(`/api/admin/channels/${channelId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('频道删除成功', 'success');
            loadChannelsList();
        } else {
            showToast(data.message || '删除频道失败', 'error');
        }
    })
    .catch(error => {
        console.error('删除频道失败:', error);
        showToast('删除频道失败', 'error');
    });
}

// 加载身份标签
function loadIdentityTags() {
    fetch('/api/admin/identity-tags')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateIdentityTags(data.tags);
            }
        })
        .catch(error => {
            console.error('加载身份标签失败:', error);
        });
}

// 更新身份标签显示
function updateIdentityTags(tags) {
    const categories = ['profession', 'personality', 'emotion', 'hobby'];
    
    categories.forEach(category => {
        const container = document.getElementById(`${category}Tags`);
        if (!container) return;
        
        const categoryTags = tags.filter(tag => tag.category === category);
        
        container.innerHTML = '';
        
        if (categoryTags.length === 0) {
            container.innerHTML = '<p class="text-muted small">暂无标签</p>';
            return;
        }
        
        categoryTags.forEach(tag => {
            const tagElement = document.createElement('div');
            tagElement.className = 'tag-item';
            tagElement.innerHTML = `
                <div class="tag-info">
                    <div class="tag-name">${tag.name}</div>
                    <div class="tag-description">${tag.description || ''}</div>
                    <div class="tag-usage">使用次数: ${tag.usage_count || 0}</div>
                </div>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteTag('${tag.id}')">
                    <i class="fas fa-times"></i>
                </button>
            `;
            container.appendChild(tagElement);
        });
    });
}

// 添加身份标签
function addIdentityTag() {
    const category = document.getElementById('tagCategory').value;
    const name = document.getElementById('tagName').value.trim();
    const description = document.getElementById('tagDescription').value.trim();
    
    if (!category || !name) {
        showToast('请填写分类和标签名称', 'warning');
        return;
    }
    
    fetch('/api/admin/identity-tags', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            category: category,
            name: name,
            description: description
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('标签添加成功', 'success');
            // 重置表单
            document.getElementById('addTagForm').reset();
            // 重新加载标签
            loadIdentityTags();
        } else {
            showToast(data.message || '添加标签失败', 'error');
        }
    })
    .catch(error => {
        console.error('添加标签失败:', error);
        showToast('添加标签失败', 'error');
    });
}

// 删除身份标签
function deleteTag(tagId) {
    if (!confirm('确定要删除这个标签吗？')) {
        return;
    }
    
    fetch(`/api/admin/identity-tags/${tagId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('标签删除成功', 'success');
            loadIdentityTags();
        } else {
            showToast(data.message || '删除标签失败', 'error');
        }
    })
    .catch(error => {
        console.error('删除标签失败:', error);
        showToast('删除标签失败', 'error');
    });
}

// 导出数据
function exportData() {
    const dataType = document.getElementById('exportType').value;
    const format = document.getElementById('exportFormat').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    if (!dataType) {
        showToast('请选择导出数据类型', 'warning');
        return;
    }
    
    const params = new URLSearchParams({
        type: dataType,
        format: format
    });
    
    if (startDate) params.append('start_date', startDate);
    if (endDate) params.append('end_date', endDate);
    
    // 创建下载链接
    const url = `/api/admin/export?${params.toString()}`;
    const link = document.createElement('a');
    link.href = url;
    link.download = `${dataType}_export.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    showToast('导出请求已发送', 'info');
}

// 加载安全信息
function loadSecurityInfo() {
    fetch('/api/admin/security')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSecurityInfo(data.info);
            }
        })
        .catch(error => {
            console.error('加载安全信息失败:', error);
        });
}

// 更新安全信息显示
function updateSecurityInfo(info) {
    const elements = {
        'blockedIPs': info.blocked_ips || 0,
        'failedAttempts': info.failed_attempts || 0,
        'lastBackup': info.last_backup || '无',
        'backupCount': info.backup_count || 0
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
}

// 创建备份
function createBackup() {
    fetch('/api/admin/backup', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('备份创建成功', 'success');
            loadSecurityInfo();
        } else {
            showToast(data.message || '创建备份失败', 'error');
        }
    })
    .catch(error => {
        console.error('创建备份失败:', error);
        showToast('创建备份失败', 'error');
    });
}

// 清理数据
function cleanupData() {
    if (!confirm('确定要清理过期数据吗？此操作不可恢复。')) {
        return;
    }
    
    fetch('/api/admin/cleanup', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('数据清理完成', 'success');
            loadSystemOverview();
        } else {
            showToast(data.message || '数据清理失败', 'error');
        }
    })
    .catch(error => {
        console.error('数据清理失败:', error);
        showToast('数据清理失败', 'error');
    });
}

// 加载问卷列表
function loadSurveysList() {
    fetch('/api/admin/surveys')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSurveysList(data.data);
            } else {
                console.error('加载问卷列表失败:', data.error);
                showToast('加载问卷列表失败', 'error');
            }
        })
        .catch(error => {
            console.error('加载问卷列表失败:', error);
            showToast('加载问卷列表失败', 'error');
        });
}

// 更新问卷列表显示
function updateSurveysList(surveys) {
    const tbody = document.querySelector('#surveys-section tbody');
    if (!tbody) return;

    tbody.innerHTML = '';

    if (surveys.length === 0) {
        tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">暂无问卷数据</td></tr>';
        return;
    }

    surveys.forEach(survey => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${survey.title}</td>
            <td>${survey.channel_name || '全局'}</td>
            <td>${survey.response_count || 0}</td>
            <td><span class="badge ${survey.is_active ? 'bg-success' : 'bg-secondary'}">${survey.is_active ? '活跃' : '已结束'}</span></td>
            <td>${new Date(survey.created_at).toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewSurveyDetails('${survey.id}')">
                    <i class="fas fa-eye"></i> 查看
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteSurvey('${survey.id}')">
                    <i class="fas fa-trash"></i> 删除
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 加载数据分析
function loadAnalyticsData() {
    fetch('/api/admin/analytics')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAnalyticsDisplay(data.data);
            } else {
                console.error('加载数据分析失败:', data.error);
                showToast('加载数据分析失败', 'error');
            }
        })
        .catch(error => {
            console.error('加载数据分析失败:', error);
            showToast('加载数据分析失败', 'error');
        });
}

// 更新数据分析显示
function updateAnalyticsDisplay(analytics) {
    // 更新统计卡片
    const statsContainer = document.querySelector('#analytics-stats');
    if (statsContainer && analytics.stats) {
        statsContainer.innerHTML = `
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">${analytics.stats.total_messages || 0}</h5>
                        <p class="card-text">总消息数</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">${analytics.stats.active_users || 0}</h5>
                        <p class="card-text">活跃用户</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">${analytics.stats.total_channels || 0}</h5>
                        <p class="card-text">频道数量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card text-center">
                    <div class="card-body">
                        <h5 class="card-title">${analytics.stats.survey_responses || 0}</h5>
                        <p class="card-text">问卷回复</p>
                    </div>
                </div>
            </div>
        `;
    }
}

// 加载安全设置
function loadSecuritySettings() {
    // 安全设置页面通常是静态的，不需要额外加载数据
    console.log('安全设置页面已加载');
}

// 加载备份列表
function loadBackupList() {
    // 备份列表可以从后端获取，这里先提供基本实现
    console.log('备份管理页面已加载');
}

// 查看问卷详情
function viewSurveyDetails(surveyId) {
    // 实现查看问卷详情的逻辑
    console.log('查看问卷详情:', surveyId);
    showToast('查看问卷详情功能待实现', 'info');
}

// 删除问卷
function deleteSurvey(surveyId) {
    if (confirm('确定要删除这个问卷吗？此操作不可恢复。')) {
        // 实现删除问卷的逻辑
        console.log('删除问卷:', surveyId);
        showToast('删除问卷功能待实现', 'info');
    }
}

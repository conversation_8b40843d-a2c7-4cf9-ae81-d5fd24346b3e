// 聊天页面JavaScript

let currentChannelId = null;
let currentMessages = [];
let currentMembers = [];
let currentSurveys = [];

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 从URL获取频道ID
    const pathParts = window.location.pathname.split('/');
    currentChannelId = pathParts[pathParts.length - 1];
    
    if (currentChannelId) {
        initializeChatPage();
    }
});

// 初始化聊天页面
function initializeChatPage() {
    loadChannelInfo();
    loadMessages();
    loadMembers();
    loadSurveys();
    initializeChatSocket();
    initializeEventListeners();
}

// 初始化聊天Socket事件
function initializeChatSocket() {
    if (!socket) return;
    
    // 加入频道
    socket.emit('join_channel', {
        channel_id: currentChannelId,
        user_id: currentUserId
    });
    
    // 监听新消息
    socket.on('new_message', function(data) {
        addMessageToChat(data.message);
        scrollToBottom();
    });
    
    // 监听成员更新
    socket.on('member_update', function(data) {
        updateMembersList(data.members);
    });
    
    // 监听身份公开
    socket.on('identity_revealed', function(data) {
        updateMemberIdentity(data.user_id, data.identity);
    });
    
    // 监听用户在线状态
    socket.on('user_status_update', function(data) {
        updateUserStatus(data.user_id, data.status);
    });
}

// 初始化事件监听器
function initializeEventListeners() {
    // 消息发送表单
    const messageForm = document.getElementById('messageForm');
    if (messageForm) {
        messageForm.addEventListener('submit', function(e) {
            e.preventDefault();
            sendMessage();
        });
    }
    
    // 图片上传
    const imageUpload = document.getElementById('imageUpload');
    if (imageUpload) {
        imageUpload.addEventListener('change', handleImageUpload);
    }
    
    // 身份公开表单
    const revealIdentityForm = document.getElementById('revealIdentityForm');
    if (revealIdentityForm) {
        revealIdentityForm.addEventListener('submit', function(e) {
            e.preventDefault();
            revealIdentity();
        });
    }
    
    // 创建问卷表单
    const createSurveyForm = document.getElementById('createSurveyForm');
    if (createSurveyForm) {
        createSurveyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createSurvey();
        });
    }
}

// 加载频道信息
function loadChannelInfo() {
    fetch(`/api/channels/${currentChannelId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateChannelInfo(data.channel);
            }
        })
        .catch(error => {
            console.error('加载频道信息失败:', error);
        });
}

// 更新频道信息显示
function updateChannelInfo(channel) {
    document.title = `${channel.name} - 多身份聊天室`;
    
    const channelName = document.getElementById('channelName');
    if (channelName) {
        channelName.textContent = channel.name;
    }
    
    const memberCount = document.getElementById('memberCount');
    if (memberCount) {
        memberCount.textContent = `${channel.member_count} 成员`;
    }
}

// 加载消息历史
function loadMessages() {
    fetch(`/api/channels/${currentChannelId}/messages`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentMessages = data.messages;
                displayMessages();
                scrollToBottom();
            }
        })
        .catch(error => {
            console.error('加载消息失败:', error);
        });
}

// 显示消息
function displayMessages() {
    const container = document.getElementById('messagesContainer');
    if (!container) return;
    
    container.innerHTML = '';
    
    currentMessages.forEach(message => {
        addMessageToChat(message);
    });
}

// 添加消息到聊天
function addMessageToChat(message) {
    const container = document.getElementById('messagesContainer');
    if (!container) return;
    
    const messageElement = createMessageElement(message);
    container.appendChild(messageElement);
}

// 创建消息元素
function createMessageElement(message) {
    const div = document.createElement('div');
    div.className = 'message';
    
    if (message.user_id === currentUserId) {
        div.classList.add('own-message');
    }
    
    let identityBadges = '';
    if (message.revealed_identities && message.revealed_identities.length > 0) {
        identityBadges = message.revealed_identities.map(identity => 
            `<span class="identity-badge">${identity.identity_name}</span>`
        ).join('');
    }
    
    let content = '';
    if (message.message_type === 'text') {
        content = `<div class="message-text">${escapeHtml(message.content)}</div>`;
    } else if (message.message_type === 'image') {
        const imageData = JSON.parse(message.content);
        content = `
            <div class="message-image">
                <img src="/uploads/thumbnails/${imageData.thumbnail}" 
                     alt="图片" 
                     onclick="showFullImage('/uploads/images/${imageData.filename}')"
                     style="cursor: pointer; max-width: 200px; border-radius: 8px;">
            </div>
        `;
    }
    
    div.innerHTML = `
        <div class="message-header">
            <span class="message-user">用户${message.user_id.substring(0, 8)}</span>
            ${identityBadges}
            <span class="message-time">${formatTime(message.created_at)}</span>
        </div>
        ${content}
    `;
    
    return div;
}

// 发送消息
function sendMessage() {
    const input = document.getElementById('messageInput');
    const content = input.value.trim();
    
    if (!content) return;
    
    const data = {
        channel_id: currentChannelId,
        content: content,
        message_type: 'text'
    };
    
    fetch('/api/messages', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            input.value = '';
        } else {
            showToast(data.message || '发送消息失败', 'error');
        }
    })
    .catch(error => {
        console.error('发送消息失败:', error);
        showToast('发送消息失败', 'error');
    });
}

// 处理图片上传
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;
    
    // 检查文件类型
    if (!file.type.startsWith('image/')) {
        showToast('请选择图片文件', 'warning');
        return;
    }
    
    // 检查文件大小 (10MB)
    if (file.size > 10 * 1024 * 1024) {
        showToast('图片文件不能超过10MB', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('image', file);
    formData.append('channel_id', currentChannelId);
    
    fetch('/api/upload/image', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('图片上传成功', 'success');
            // 清空文件输入
            event.target.value = '';
        } else {
            showToast(data.message || '图片上传失败', 'error');
        }
    })
    .catch(error => {
        console.error('图片上传失败:', error);
        showToast('图片上传失败', 'error');
    });
}

// 显示全尺寸图片
function showFullImage(imagePath) {
    const modal = document.getElementById('imageModal');
    const modalImage = document.getElementById('modalImage');
    
    modalImage.src = imagePath;
    
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();
}

// 加载成员列表
function loadMembers() {
    fetch(`/api/channels/${currentChannelId}/members`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                currentMembers = data.members;
                updateMembersList(data.members);
            }
        })
        .catch(error => {
            console.error('加载成员列表失败:', error);
        });
}

// 更新成员列表
function updateMembersList(members) {
    const container = document.getElementById('membersList');
    if (!container) return;
    
    container.innerHTML = '';
    
    members.forEach(member => {
        const memberElement = createMemberElement(member);
        container.appendChild(memberElement);
    });
}

// 创建成员元素
function createMemberElement(member) {
    const div = document.createElement('div');
    div.className = 'member-item';
    
    let identityBadges = '';
    if (member.revealed_identities && member.revealed_identities.length > 0) {
        identityBadges = member.revealed_identities.map(identity => 
            `<span class="identity-badge small">${identity.identity_name}</span>`
        ).join('');
    }
    
    const statusIcon = member.is_online ? 
        '<i class="fas fa-circle text-success"></i>' : 
        '<i class="fas fa-circle text-muted"></i>';
    
    div.innerHTML = `
        <div class="member-info">
            <div class="member-name">
                ${statusIcon}
                用户${member.user_id.substring(0, 8)}
            </div>
            <div class="member-identities">${identityBadges}</div>
        </div>
    `;
    
    return div;
}

// 显示身份公开模态框
function showRevealIdentityModal() {
    loadAvailableIdentities();
    const modal = new bootstrap.Modal(document.getElementById('revealIdentityModal'));
    modal.show();
}

// 加载可公开的身份
function loadAvailableIdentities() {
    fetch(`/api/channels/${currentChannelId}/available-identities`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateAvailableIdentities(data.identities);
            }
        })
        .catch(error => {
            console.error('加载可用身份失败:', error);
        });
}

// 更新可用身份列表
function updateAvailableIdentities(identities) {
    const container = document.getElementById('availableIdentities');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (identities.length === 0) {
        container.innerHTML = '<p class="text-muted">没有可公开的身份</p>';
        return;
    }
    
    identities.forEach(identity => {
        const div = document.createElement('div');
        div.className = 'form-check';
        div.innerHTML = `
            <input class="form-check-input" type="checkbox" value="${identity.id}" id="identity_${identity.id}">
            <label class="form-check-label" for="identity_${identity.id}">
                ${identity.identity_name} (${getCategoryName(identity.category)})
            </label>
        `;
        container.appendChild(div);
    });
}

// 公开身份
function revealIdentity() {
    const checkboxes = document.querySelectorAll('#availableIdentities input[type="checkbox"]:checked');
    const identityIds = Array.from(checkboxes).map(cb => cb.value);
    
    if (identityIds.length === 0) {
        showToast('请选择要公开的身份', 'warning');
        return;
    }
    
    fetch(`/api/channels/${currentChannelId}/reveal-identity`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            identity_ids: identityIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('身份公开成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('revealIdentityModal'));
            modal.hide();
            // 重新加载成员列表
            loadMembers();
        } else {
            showToast(data.message || '身份公开失败', 'error');
        }
    })
    .catch(error => {
        console.error('身份公开失败:', error);
        showToast('身份公开失败', 'error');
    });
}

// 滚动到底部
function scrollToBottom() {
    const container = document.getElementById('messagesContainer');
    if (container) {
        container.scrollTop = container.scrollHeight;
    }
}

// HTML转义
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全管理模块
"""

import time
import uuid
import hashlib
import os
import shutil
from datetime import datetime, timedelta
from collections import defaultdict
from functools import wraps
from flask import request, session, jsonify
import json

class SecurityManager:
    """安全管理器"""
    
    def __init__(self, config):
        self.config = config
        self.rate_limits = defaultdict(list)  # {user_id: [timestamp, ...]}
        self.upload_limits = defaultdict(list)  # {user_id: [timestamp, ...]}
        self.failed_attempts = defaultdict(int)  # {ip: count}
        self.blocked_ips = set()
    
    def rate_limit_decorator(self, limit_type='message'):
        """速率限制装饰器"""
        def decorator(f):
            @wraps(f)
            def decorated_function(*args, **kwargs):
                user_id = session.get('user_id')
                if not user_id:
                    return jsonify({'error': '未授权访问'}), 401
                
                if not self.check_rate_limit(user_id, limit_type):
                    return jsonify({'error': '操作过于频繁，请稍后再试'}), 429
                
                return f(*args, **kwargs)
            return decorated_function
        return decorator
    
    def check_rate_limit(self, user_id, limit_type='message'):
        """检查速率限制"""
        current_time = time.time()
        
        if limit_type == 'message':
            limit = self.config.getint('DEFAULT', 'RATE_LIMIT_MESSAGES', fallback=20)
            window = 60  # 1分钟
            user_requests = self.rate_limits[user_id]
        elif limit_type == 'upload':
            limit = self.config.getint('DEFAULT', 'RATE_LIMIT_UPLOADS', fallback=5)
            window = 3600  # 1小时
            user_requests = self.upload_limits[user_id]
        else:
            return True
        
        # 清理过期的请求记录
        user_requests[:] = [req_time for req_time in user_requests if current_time - req_time < window]
        
        # 检查是否超过限制
        if len(user_requests) >= limit:
            return False
        
        # 记录当前请求
        user_requests.append(current_time)
        return True
    
    def validate_input(self, data, schema):
        """输入验证"""
        if not isinstance(data, dict):
            return False, "数据格式错误"
        
        for field, rules in schema.items():
            if rules.get('required', False) and field not in data:
                return False, f"缺少必需字段: {field}"
            
            if field in data:
                value = data[field]
                
                # 类型检查
                if 'type' in rules:
                    expected_type = rules['type']
                    if expected_type == 'string' and not isinstance(value, str):
                        return False, f"字段 {field} 必须是字符串"
                    elif expected_type == 'int' and not isinstance(value, int):
                        return False, f"字段 {field} 必须是整数"
                    elif expected_type == 'list' and not isinstance(value, list):
                        return False, f"字段 {field} 必须是列表"
                
                # 长度检查
                if 'max_length' in rules and isinstance(value, str):
                    if len(value) > rules['max_length']:
                        return False, f"字段 {field} 长度不能超过 {rules['max_length']}"
                
                if 'min_length' in rules and isinstance(value, str):
                    if len(value) < rules['min_length']:
                        return False, f"字段 {field} 长度不能少于 {rules['min_length']}"
                
                # 值范围检查
                if 'min_value' in rules and isinstance(value, (int, float)):
                    if value < rules['min_value']:
                        return False, f"字段 {field} 值不能小于 {rules['min_value']}"
                
                if 'max_value' in rules and isinstance(value, (int, float)):
                    if value > rules['max_value']:
                        return False, f"字段 {field} 值不能大于 {rules['max_value']}"
                
                # 枚举值检查
                if 'choices' in rules:
                    if value not in rules['choices']:
                        return False, f"字段 {field} 值必须是 {rules['choices']} 中的一个"
        
        return True, "验证通过"
    
    def validate_image_file(self, file):
        """验证图片文件"""
        if not file or not file.filename:
            return False, "没有选择文件"
        
        # 检查文件扩展名
        allowed_extensions = self.config.get('DEFAULT', 'ALLOWED_EXTENSIONS', fallback='jpg,jpeg,png,gif').split(',')
        file_ext = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''
        
        if file_ext not in allowed_extensions:
            return False, f"不支持的文件格式，仅支持: {', '.join(allowed_extensions)}"
        
        # 检查文件大小
        max_size = self.config.getint('DEFAULT', 'MAX_CONTENT_LENGTH', fallback=10485760)  # 10MB
        file.seek(0, 2)  # 移动到文件末尾
        file_size = file.tell()
        file.seek(0)  # 重置文件指针
        
        if file_size > max_size:
            return False, f"文件大小不能超过 {max_size // 1024 // 1024}MB"
        
        # 检查文件头（魔数）
        file_header = file.read(16)
        file.seek(0)  # 重置文件指针
        
        # 常见图片格式的文件头
        image_signatures = {
            b'\xff\xd8\xff': 'jpg',
            b'\x89PNG\r\n\x1a\n': 'png',
            b'GIF87a': 'gif',
            b'GIF89a': 'gif'
        }
        
        is_valid_image = False
        for signature, format_name in image_signatures.items():
            if file_header.startswith(signature):
                is_valid_image = True
                break
        
        if not is_valid_image:
            return False, "文件不是有效的图片格式"
        
        return True, "文件验证通过"
    
    def log_audit_event(self, db_manager, user_id, action, target_type=None, target_id=None, details=None):
        """记录审计日志"""
        try:
            audit_id = str(uuid.uuid4())
            ip_address = request.remote_addr if request else None
            user_agent = request.headers.get('User-Agent') if request else None
            
            details_json = json.dumps(details, ensure_ascii=False) if details else None
            
            db_manager.execute_query('''
                INSERT INTO audit_logs (id, user_id, action, target_type, target_id, details, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (audit_id, user_id, action, target_type, target_id, details_json, ip_address, user_agent))
            
        except Exception as e:
            print(f"记录审计日志失败: {e}")
    
    def check_ip_blocked(self, ip_address):
        """检查IP是否被封禁"""
        return ip_address in self.blocked_ips
    
    def record_failed_attempt(self, ip_address):
        """记录失败尝试"""
        self.failed_attempts[ip_address] += 1
        
        # 如果失败次数过多，封禁IP
        if self.failed_attempts[ip_address] >= 10:
            self.blocked_ips.add(ip_address)
            print(f"IP {ip_address} 已被封禁（失败尝试过多）")
    
    def reset_failed_attempts(self, ip_address):
        """重置失败尝试计数"""
        if ip_address in self.failed_attempts:
            del self.failed_attempts[ip_address]
    
    def generate_secure_filename(self, original_filename):
        """生成安全的文件名"""
        # 获取文件扩展名
        file_ext = ''
        if '.' in original_filename:
            file_ext = '.' + original_filename.rsplit('.', 1)[1].lower()
        
        # 生成UUID作为文件名
        secure_name = str(uuid.uuid4()) + file_ext
        return secure_name
    
    def sanitize_content(self, content):
        """内容清理（防止XSS等）"""
        if not isinstance(content, str):
            return content
        
        # 基本的HTML标签清理
        dangerous_tags = ['<script', '</script>', '<iframe', '</iframe>', '<object', '</object>']
        cleaned_content = content
        
        for tag in dangerous_tags:
            cleaned_content = cleaned_content.replace(tag.lower(), '')
            cleaned_content = cleaned_content.replace(tag.upper(), '')
        
        return cleaned_content
    
    def create_backup(self, db_path, backup_folder):
        """创建数据库备份"""
        try:
            # 确保备份目录存在
            os.makedirs(backup_folder, exist_ok=True)
            
            # 生成备份文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(backup_folder, backup_filename)
            
            # 复制数据库文件
            shutil.copy2(db_path, backup_path)
            
            # 清理旧备份（保留最近7天）
            self._cleanup_old_backups(backup_folder)
            
            return True, backup_filename
        except Exception as e:
            return False, f"创建备份失败: {e}"
    
    def _cleanup_old_backups(self, backup_folder):
        """清理旧备份文件"""
        try:
            retention_days = self.config.getint('DEFAULT', 'BACKUP_RETENTION_DAYS', fallback=7)
            cutoff_time = datetime.now() - timedelta(days=retention_days)
            
            for filename in os.listdir(backup_folder):
                if filename.startswith('backup_') and filename.endswith('.db'):
                    file_path = os.path.join(backup_folder, filename)
                    file_time = datetime.fromtimestamp(os.path.getctime(file_path))
                    
                    if file_time < cutoff_time:
                        os.remove(file_path)
                        print(f"已删除旧备份文件: {filename}")
        except Exception as e:
            print(f"清理旧备份失败: {e}")
    
    def get_security_report(self, db_manager):
        """获取安全报告"""
        try:
            # 最近的审计日志
            recent_logs = db_manager.execute_query('''
                SELECT action, COUNT(*) as count
                FROM audit_logs 
                WHERE created_at >= datetime('now', '-7 days')
                GROUP BY action
                ORDER BY count DESC
            ''')
            
            # 失败尝试统计
            failed_attempts_summary = {
                'total_blocked_ips': len(self.blocked_ips),
                'failed_attempts_by_ip': dict(self.failed_attempts),
                'blocked_ips': list(self.blocked_ips)
            }
            
            # 速率限制统计
            rate_limit_summary = {
                'active_message_limits': len([u for u, reqs in self.rate_limits.items() if reqs]),
                'active_upload_limits': len([u for u, reqs in self.upload_limits.items() if reqs])
            }
            
            return {
                'recent_audit_logs': [dict(row) for row in recent_logs],
                'failed_attempts': failed_attempts_summary,
                'rate_limits': rate_limit_summary,
                'generated_at': datetime.now().isoformat()
            }
        except Exception as e:
            print(f"生成安全报告失败: {e}")
            return None

class BackupManager:
    """备份管理器"""
    
    def __init__(self, config):
        self.config = config
        self.backup_folder = config.get('DEFAULT', 'BACKUP_FOLDER', fallback='backups')
    
    def schedule_daily_backup(self, db_path):
        """计划每日备份（这里只是示例，实际应该使用任务调度器）"""
        # 在实际部署中，应该使用Windows任务计划程序或其他调度工具
        pass
    
    def restore_from_backup(self, backup_filename, target_db_path):
        """从备份恢复数据库"""
        try:
            backup_path = os.path.join(self.backup_folder, backup_filename)
            
            if not os.path.exists(backup_path):
                return False, "备份文件不存在"
            
            # 备份当前数据库
            if os.path.exists(target_db_path):
                current_backup = target_db_path + '.before_restore'
                shutil.copy2(target_db_path, current_backup)
            
            # 恢复备份
            shutil.copy2(backup_path, target_db_path)
            
            return True, "数据库恢复成功"
        except Exception as e:
            return False, f"恢复数据库失败: {e}"
    
    def list_backups(self):
        """列出所有备份文件"""
        try:
            backups = []
            if os.path.exists(self.backup_folder):
                for filename in os.listdir(self.backup_folder):
                    if filename.startswith('backup_') and filename.endswith('.db'):
                        file_path = os.path.join(self.backup_folder, filename)
                        file_stat = os.stat(file_path)
                        
                        backups.append({
                            'filename': filename,
                            'size': file_stat.st_size,
                            'created_at': datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                            'modified_at': datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                        })
            
            return sorted(backups, key=lambda x: x['created_at'], reverse=True)
        except Exception as e:
            print(f"列出备份文件失败: {e}")
            return []

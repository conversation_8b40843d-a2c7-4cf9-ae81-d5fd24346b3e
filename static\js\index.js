// 首页JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadChannels();
    loadSystemStats();
    initializeEventListeners();
});

// 初始化事件监听器
function initializeEventListeners() {
    // 创建频道表单
    const createChannelForm = document.getElementById('createChannelForm');
    if (createChannelForm) {
        createChannelForm.addEventListener('submit', function(e) {
            e.preventDefault();
            createChannel();
        });
    }
    
    // 加入频道表单
    const joinChannelForm = document.getElementById('joinChannelForm');
    if (joinChannelForm) {
        joinChannelForm.addEventListener('submit', function(e) {
            e.preventDefault();
            joinChannel();
        });
    }
}

// 加载频道列表
function loadChannels() {
    // 加载我的频道
    fetch('/api/channels/my')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayMyChannels(data.channels);
            }
        })
        .catch(error => {
            console.error('加载我的频道失败:', error);
        });
    
    // 加载公开频道
    fetch('/api/channels/public')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayPublicChannels(data.channels);
            }
        })
        .catch(error => {
            console.error('加载公开频道失败:', error);
        });
}

// 显示我的频道
function displayMyChannels(channels) {
    const container = document.getElementById('myChannels');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (channels.length === 0) {
        container.innerHTML = '<p class="text-muted small">还没有加入任何频道</p>';
        return;
    }
    
    channels.forEach(channel => {
        const channelElement = createChannelElement(channel, true);
        container.appendChild(channelElement);
    });
}

// 显示公开频道
function displayPublicChannels(channels) {
    const container = document.getElementById('publicChannels');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (channels.length === 0) {
        container.innerHTML = '<p class="text-muted small">暂无公开频道</p>';
        return;
    }
    
    channels.forEach(channel => {
        const channelElement = createChannelElement(channel, false);
        container.appendChild(channelElement);
    });
}

// 创建频道元素
function createChannelElement(channel, isJoined) {
    const div = document.createElement('div');
    div.className = 'channel-item';
    
    let typeIcon = '';
    let typeText = '';
    switch (channel.type) {
        case 'public':
            typeIcon = '<i class="fas fa-globe text-success"></i>';
            typeText = '公开';
            break;
        case 'password':
            typeIcon = '<i class="fas fa-lock text-warning"></i>';
            typeText = '密码';
            break;
        case 'invite':
            typeIcon = '<i class="fas fa-user-lock text-info"></i>';
            typeText = '邀请';
            break;
    }
    
    div.innerHTML = `
        <div class="channel-info">
            <div class="channel-name">${channel.name}</div>
            <div class="channel-meta">
                ${typeText} • ${channel.member_count} 成员
                ${channel.max_members > 0 ? ' / ' + channel.max_members : ''}
            </div>
        </div>
        <div class="channel-type-icon">
            ${typeIcon}
        </div>
    `;
    
    if (isJoined) {
        div.onclick = () => enterChannel(channel.id);
    } else {
        div.onclick = () => showJoinChannelModal(channel);
    }
    
    return div;
}

// 进入频道
function enterChannel(channelId) {
    window.location.href = `/chat/${channelId}`;
}

// 显示创建频道模态框
function showCreateChannelModal() {
    const modal = new bootstrap.Modal(document.getElementById('createChannelModal'));
    modal.show();
}

// 切换密码字段显示
function togglePasswordField() {
    const channelType = document.getElementById('channelType').value;
    const passwordField = document.getElementById('passwordField');
    
    if (channelType === 'password') {
        passwordField.style.display = 'block';
        document.getElementById('channelPassword').required = true;
    } else {
        passwordField.style.display = 'none';
        document.getElementById('channelPassword').required = false;
    }
}

// 创建频道
function createChannel() {
    const name = document.getElementById('channelName').value.trim();
    const type = document.getElementById('channelType').value;
    const password = document.getElementById('channelPassword').value;
    const maxMembers = parseInt(document.getElementById('maxMembers').value) || 0;
    
    if (!name) {
        showToast('请输入频道名称', 'warning');
        return;
    }
    
    if (type === 'password' && !password) {
        showToast('密码频道需要设置密码', 'warning');
        return;
    }
    
    const data = {
        name: name,
        type: type,
        max_members: maxMembers
    };
    
    if (type === 'password') {
        data.password = password;
    }
    
    fetch('/api/channels', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('频道创建成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('createChannelModal'));
            modal.hide();
            // 重置表单
            document.getElementById('createChannelForm').reset();
            togglePasswordField();
            // 重新加载频道列表
            loadChannels();
        } else {
            showToast(data.message || '创建频道失败', 'error');
        }
    })
    .catch(error => {
        console.error('创建频道失败:', error);
        showToast('创建频道失败', 'error');
    });
}

// 显示加入频道模态框
function showJoinChannelModal(channel) {
    const modal = new bootstrap.Modal(document.getElementById('joinChannelModal'));
    const infoDiv = document.getElementById('joinChannelInfo');
    const passwordField = document.getElementById('joinPasswordField');
    
    // 设置频道信息
    document.getElementById('joinChannelId').value = channel.id;
    
    let typeText = '';
    switch (channel.type) {
        case 'public':
            typeText = '公开频道';
            break;
        case 'password':
            typeText = '密码频道';
            break;
        case 'invite':
            typeText = '邀请制频道';
            break;
    }
    
    infoDiv.innerHTML = `
        <div class="mb-3">
            <h6>${channel.name}</h6>
            <p class="text-muted">
                ${typeText} • ${channel.member_count} 成员
                ${channel.max_members > 0 ? ' / ' + channel.max_members : ''}
            </p>
        </div>
    `;
    
    // 显示/隐藏密码字段
    if (channel.type === 'password') {
        passwordField.style.display = 'block';
        document.getElementById('joinChannelPassword').required = true;
    } else {
        passwordField.style.display = 'none';
        document.getElementById('joinChannelPassword').required = false;
    }
    
    modal.show();
}

// 加入频道
function joinChannel() {
    const channelId = document.getElementById('joinChannelId').value;
    const password = document.getElementById('joinChannelPassword').value;
    
    const data = { channel_id: channelId };
    if (password) {
        data.password = password;
    }
    
    fetch('/api/channels/join', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('加入频道成功', 'success');
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('joinChannelModal'));
            modal.hide();
            // 重置表单
            document.getElementById('joinChannelForm').reset();
            // 重新加载频道列表
            loadChannels();
        } else {
            showToast(data.message || '加入频道失败', 'error');
        }
    })
    .catch(error => {
        console.error('加入频道失败:', error);
        showToast('加入频道失败', 'error');
    });
}

// 加载系统统计
function loadSystemStats() {
    fetch('/api/system/stats')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateSystemStats(data.stats);
            }
        })
        .catch(error => {
            console.error('加载系统统计失败:', error);
        });
}

// 更新系统统计显示
function updateSystemStats(stats) {
    const elements = {
        'totalChannels': stats.total_channels || 0,
        'onlineUsers': stats.online_users || 0,
        'totalMessages': stats.total_messages || 0,
        'activeSurveys': stats.active_surveys || 0
    };
    
    Object.keys(elements).forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = elements[id];
        }
    });
}

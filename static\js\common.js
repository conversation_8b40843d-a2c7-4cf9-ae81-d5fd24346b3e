// 多身份聊天室系统 - 通用JavaScript函数

// 全局变量
let socket = null;
let currentUserId = null;
let userIdentities = [];

// 初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeUser();
    initializeSocket();
    loadUserIdentities();
});

// 初始化用户
function initializeUser() {
    // 检查是否有用户ID cookie
    currentUserId = getCookie('user_id');
    if (!currentUserId) {
        // 生成新的用户ID
        currentUserId = generateUUID();
        setCookie('user_id', currentUserId, 730); // 2年过期
    }
}

// 初始化Socket连接
function initializeSocket() {
    socket = io();
    
    socket.on('connect', function() {
        console.log('Socket连接成功');
        socket.emit('user_connect', {user_id: currentUserId});
    });
    
    socket.on('disconnect', function() {
        console.log('Socket连接断开');
    });
    
    socket.on('error', function(error) {
        console.error('Socket错误:', error);
        showToast('连接错误: ' + error, 'error');
    });
}

// 加载用户身份
function loadUserIdentities() {
    fetch('/api/user/identities')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                userIdentities = data.identities;
                updateIdentityDisplay();
            }
        })
        .catch(error => {
            console.error('加载身份失败:', error);
        });
}

// 更新身份显示
function updateIdentityDisplay() {
    const container = document.getElementById('currentIdentities');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (userIdentities.length === 0) {
        container.innerHTML = '<p class="text-muted small">还没有设置身份</p>';
        return;
    }
    
    userIdentities.forEach(identity => {
        const identityElement = document.createElement('div');
        identityElement.className = 'identity-item';
        identityElement.innerHTML = `
            <div>
                <div class="identity-name">${identity.identity_name}</div>
                <div class="identity-category">${getCategoryName(identity.category)}</div>
            </div>
            <button class="btn btn-sm btn-outline-danger" onclick="removeIdentity('${identity.id}')">
                <i class="fas fa-times"></i>
            </button>
        `;
        container.appendChild(identityElement);
    });
}

// 显示身份管理模态框
function showIdentityModal() {
    loadUserIdentities();
    const modal = new bootstrap.Modal(document.getElementById('identityModal'));
    modal.show();
}

// 加载身份选项
function loadIdentityOptions() {
    const category = document.getElementById('identityCategory').value;
    const nameSelect = document.getElementById('identityName');
    
    if (!category) {
        nameSelect.innerHTML = '<option value="">请先选择分类</option>';
        return;
    }
    
    fetch(`/api/identity-tags/${category}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                nameSelect.innerHTML = '<option value="">请选择身份</option>';
                data.tags.forEach(tag => {
                    const option = document.createElement('option');
                    option.value = tag.name;
                    option.textContent = tag.name;
                    if (tag.description) {
                        option.title = tag.description;
                    }
                    nameSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('加载身份选项失败:', error);
        });
}

// 添加身份表单处理
document.addEventListener('DOMContentLoaded', function() {
    const addIdentityForm = document.getElementById('addIdentityForm');
    if (addIdentityForm) {
        addIdentityForm.addEventListener('submit', function(e) {
            e.preventDefault();
            addIdentity();
        });
    }
});

// 添加身份
function addIdentity() {
    const category = document.getElementById('identityCategory').value;
    const identityName = document.getElementById('identityName').value;
    
    if (!category || !identityName) {
        showToast('请选择分类和身份', 'warning');
        return;
    }
    
    // 检查是否已达到上限
    if (userIdentities.length >= 3) {
        showToast('每个用户最多只能设置3个身份', 'warning');
        return;
    }
    
    // 检查是否已存在
    const exists = userIdentities.some(identity => 
        identity.category === category && identity.identity_name === identityName
    );
    
    if (exists) {
        showToast('该身份已存在', 'warning');
        return;
    }
    
    fetch('/api/user/identities', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            category: category,
            identity_name: identityName
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('身份添加成功', 'success');
            loadUserIdentities();
            // 重置表单
            document.getElementById('addIdentityForm').reset();
            document.getElementById('identityName').innerHTML = '<option value="">请先选择分类</option>';
        } else {
            showToast(data.message || '添加身份失败', 'error');
        }
    })
    .catch(error => {
        console.error('添加身份失败:', error);
        showToast('添加身份失败', 'error');
    });
}

// 移除身份
function removeIdentity(identityId) {
    if (!confirm('确定要移除这个身份吗？')) {
        return;
    }
    
    fetch(`/api/user/identities/${identityId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('身份移除成功', 'success');
            loadUserIdentities();
        } else {
            showToast(data.message || '移除身份失败', 'error');
        }
    })
    .catch(error => {
        console.error('移除身份失败:', error);
        showToast('移除身份失败', 'error');
    });
}

// 显示Toast通知
function showToast(message, type = 'info') {
    const toast = document.getElementById('toastNotification');
    const toastMessage = document.getElementById('toastMessage');
    const toastHeader = toast.querySelector('.toast-header i');
    
    // 设置消息
    toastMessage.textContent = message;
    
    // 设置图标和颜色
    toastHeader.className = 'me-2';
    switch (type) {
        case 'success':
            toastHeader.className += ' fas fa-check-circle text-success';
            break;
        case 'error':
            toastHeader.className += ' fas fa-exclamation-circle text-danger';
            break;
        case 'warning':
            toastHeader.className += ' fas fa-exclamation-triangle text-warning';
            break;
        default:
            toastHeader.className += ' fas fa-info-circle text-primary';
    }
    
    // 显示Toast
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();
}

// 工具函数
function generateUUID() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c == 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
}

function getCookie(name) {
    const value = `; ${document.cookie}`;
    const parts = value.split(`; ${name}=`);
    if (parts.length === 2) return parts.pop().split(';').shift();
    return null;
}

function setCookie(name, value, days) {
    const expires = new Date();
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
}

function getCategoryName(category) {
    const categoryNames = {
        'profession': '职业',
        'personality': '性格',
        'emotion': '情感状况',
        'hobby': '兴趣爱好'
    };
    return categoryNames[category] || category;
}

function formatTime(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) { // 1分钟内
        return '刚刚';
    } else if (diff < 3600000) { // 1小时内
        return Math.floor(diff / 60000) + '分钟前';
    } else if (diff < 86400000) { // 1天内
        return Math.floor(diff / 3600000) + '小时前';
    } else {
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

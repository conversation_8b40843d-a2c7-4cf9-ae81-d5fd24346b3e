{% extends "base.html" %}

{% block title %}{{ channel.name }} - 聊天室{% endblock %}

{% block content %}
<div class="row h-100">
    <!-- 左侧：聊天区域 -->
    <div class="col-md-9">
        <div class="card h-100">
            <!-- 频道信息头部 -->
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="mb-0">
                        <i class="fas fa-hashtag"></i> {{ channel.name }}
                        {% if channel.type == 'password' %}
                            <i class="fas fa-lock text-warning" title="密码频道"></i>
                        {% elif channel.type == 'invite' %}
                            <i class="fas fa-user-lock text-info" title="邀请制频道"></i>
                        {% endif %}
                    </h5>
                    <small class="text-muted">
                        成员: <span id="memberCount">{{ channel.member_count }}</span>
                        {% if channel.max_members > 0 %} / {{ channel.max_members }}{% endif %}
                    </small>
                </div>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-primary" onclick="showSurveyModal()">
                        <i class="fas fa-poll"></i> 问卷
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="showIdentityRevealModal()">
                        <i class="fas fa-user-tag"></i> 公开身份
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="leaveChannel()">
                        <i class="fas fa-sign-out-alt"></i> 离开
                    </button>
                </div>
            </div>
            
            <!-- 消息区域 -->
            <div class="card-body p-0 d-flex flex-column">
                <div id="messagesContainer" class="flex-grow-1 overflow-auto p-3" style="height: 400px;">
                    <!-- 消息将在这里动态加载 -->
                </div>
                
                <!-- 输入区域 -->
                <div class="border-top p-3">
                    <form id="messageForm" class="d-flex gap-2">
                        <input type="text" class="form-control" id="messageInput" 
                               placeholder="输入消息..." maxlength="1000">
                        <input type="file" id="imageInput" accept="image/*" style="display: none;" 
                               onchange="handleImageSelect()">
                        <button type="button" class="btn btn-outline-secondary" onclick="document.getElementById('imageInput').click()">
                            <i class="fas fa-image"></i>
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </form>
                    
                    <!-- 图片预览 -->
                    <div id="imagePreview" class="mt-2" style="display: none;">
                        <div class="d-flex align-items-center gap-2">
                            <img id="previewImage" class="rounded" style="max-height: 60px;">
                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="clearImagePreview()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧：成员列表和身份信息 -->
    <div class="col-md-3">
        <!-- 在线成员 -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-users"></i> 在线成员 
                    <span class="badge bg-success" id="onlineCount">0</span>
                </h6>
            </div>
            <div class="card-body p-2">
                <div id="onlineMembers">
                    <!-- 动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 我的身份 -->
        <div class="card mb-3">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-id-card"></i> 我的身份
                </h6>
            </div>
            <div class="card-body p-2">
                <div id="myIdentities">
                    <!-- 动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 频道问卷 -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-poll"></i> 频道问卷
                </h6>
            </div>
            <div class="card-body p-2">
                <div id="channelSurveys">
                    <!-- 动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 身份公开模态框 -->
<div class="modal fade" id="identityRevealModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-tag"></i> 公开身份
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p class="text-muted">选择要在此频道公开的身份：</p>
                <div id="availableIdentities">
                    <!-- 动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 问卷模态框 -->
<div class="modal fade" id="surveyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-poll"></i> 问卷管理
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <ul class="nav nav-tabs" id="surveyTabs" role="tablist">
                    <li class="nav-item" role="presentation">
                        <button class="nav-link active" id="survey-list-tab" data-bs-toggle="tab" 
                                data-bs-target="#survey-list" type="button">问卷列表</button>
                    </li>
                    <li class="nav-item" role="presentation">
                        <button class="nav-link" id="create-survey-tab" data-bs-toggle="tab" 
                                data-bs-target="#create-survey" type="button">创建问卷</button>
                    </li>
                </ul>
                
                <div class="tab-content mt-3" id="surveyTabContent">
                    <!-- 问卷列表 -->
                    <div class="tab-pane fade show active" id="survey-list">
                        <div id="surveyList">
                            <!-- 动态加载 -->
                        </div>
                    </div>
                    
                    <!-- 创建问卷 -->
                    <div class="tab-pane fade" id="create-survey">
                        <form id="createSurveyForm">
                            <div class="mb-3">
                                <label class="form-label">问卷标题</label>
                                <input type="text" class="form-control" id="surveyTitle" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">问卷描述</label>
                                <textarea class="form-control" id="surveyDescription" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">过期时间（小时，留空表示不过期）</label>
                                <input type="number" class="form-control" id="surveyExpires" min="1" max="720">
                            </div>
                            
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center">
                                    <label class="form-label">问题列表</label>
                                    <button type="button" class="btn btn-sm btn-primary" onclick="addQuestion()">
                                        <i class="fas fa-plus"></i> 添加问题
                                    </button>
                                </div>
                                <div id="questionsList">
                                    <!-- 动态添加问题 -->
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 创建问卷
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图片查看模态框 -->
<div class="modal fade" id="imageViewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">图片查看</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="fullImage" class="img-fluid">
            </div>
        </div>
    </div>
</div>

<script>
// 传递频道信息到JavaScript
window.channelInfo = {
    id: '{{ channel.id }}',
    name: '{{ channel.name }}',
    type: '{{ channel.type }}'
};

window.userIdentities = {{ user_identities | tojsonfilter | safe }};
</script>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/chat.js') }}"></script>
{% endblock %}

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试管理后台导航功能
"""

import requests
import json

def test_admin_navigation():
    """测试管理后台导航功能"""
    base_url = "http://localhost:5000"
    session = requests.Session()
    
    print("测试管理后台导航功能...")
    print("=" * 50)
    
    # 1. 先登录管理员
    print("1. 管理员登录...")
    try:
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        response = session.post(f"{base_url}/admin/login", data=login_data, allow_redirects=False)
        if response.status_code == 302:
            print("✓ 管理员登录成功")
        else:
            print("✗ 管理员登录失败")
            return
    except Exception as e:
        print(f"✗ 登录异常: {e}")
        return
    
    print()
    
    # 2. 测试各个管理后台API
    api_tests = [
        ('/api/admin/overview', '系统概览'),
        ('/api/admin/channels', '频道管理'),
        ('/api/admin/identities', '身份库管理'),
        ('/api/admin/surveys', '问卷管理'),
        ('/api/admin/analytics', '数据分析'),
    ]
    
    for api_url, name in api_tests:
        print(f"2. 测试{name}API...")
        try:
            response = session.get(f"{base_url}{api_url}")
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✓ {name}API正常工作")
                    # 显示部分数据
                    if 'data' in data:
                        print(f"数据类型: {type(data['data'])}")
                        if isinstance(data['data'], dict):
                            print(f"数据键: {list(data['data'].keys())}")
                        elif isinstance(data['data'], list):
                            print(f"数据长度: {len(data['data'])}")
                else:
                    print(f"✗ {name}API返回失败: {data.get('error', '未知错误')}")
            else:
                print(f"✗ {name}API访问失败")
        except Exception as e:
            print(f"✗ {name}API测试异常: {e}")
        
        print()
    
    # 3. 测试导出功能
    print("3. 测试数据导出功能...")
    export_types = ['chat', 'survey', 'user']
    
    for export_type in export_types:
        try:
            response = session.get(f"{base_url}/api/admin/export/{export_type}")
            print(f"{export_type}导出状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✓ {export_type}数据导出功能正常")
                else:
                    print(f"✗ {export_type}数据导出失败: {data.get('error', '未知错误')}")
            else:
                print(f"✗ {export_type}数据导出API访问失败")
        except Exception as e:
            print(f"✗ {export_type}数据导出测试异常: {e}")
    
    print()
    
    # 4. 测试备份功能
    print("4. 测试备份功能...")
    try:
        response = session.post(f"{base_url}/api/admin/backup")
        print(f"备份状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                print("✓ 备份功能正常")
                print(f"备份信息: {data.get('message', '')}")
            else:
                print(f"✗ 备份失败: {data.get('error', '未知错误')}")
        else:
            print("✗ 备份API访问失败")
    except Exception as e:
        print(f"✗ 备份测试异常: {e}")
    
    print()
    print("=" * 50)
    print("管理后台导航功能测试完成")

if __name__ == "__main__":
    test_admin_navigation()

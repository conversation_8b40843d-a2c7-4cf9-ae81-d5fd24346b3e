#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问卷系统模块
"""

import uuid
import json
from datetime import datetime, timedelta
from collections import Counter
import pandas as pd

class SurveySystem:
    """问卷系统"""
    
    QUESTION_TYPES = {
        'SINGLE': '单选',
        'MULTI': '多选', 
        'TEXT': '文本输入',
        'LABEL': '信息标签'
    }
    
    def __init__(self, db_manager):
        self.db = db_manager
    
    def create_survey(self, creator_id, channel_id, title, description, questions, expires_hours=None):
        """创建问卷"""
        survey_id = str(uuid.uuid4())
        
        # 验证问题格式
        if not self._validate_questions(questions):
            return False, None, "问题格式不正确"
        
        questions_json = json.dumps(questions, ensure_ascii=False)
        expires_at = None
        
        if expires_hours:
            expires_at = datetime.now() + timedelta(hours=expires_hours)
        
        try:
            self.db.execute_query('''
                INSERT INTO surveys (id, channel_id, title, description, questions, created_by, expires_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (survey_id, channel_id, title, description, questions_json, creator_id, expires_at))
            
            return True, survey_id, "问卷创建成功"
        except Exception as e:
            return False, None, f"创建问卷失败: {e}"
    
    def _validate_questions(self, questions):
        """验证问题格式"""
        if not isinstance(questions, list) or len(questions) == 0:
            return False
        
        for question in questions:
            if not isinstance(question, dict):
                return False
            
            required_fields = ['type', 'title']
            if not all(field in question for field in required_fields):
                return False
            
            if question['type'] not in self.QUESTION_TYPES:
                return False
            
            # 单选和多选题需要选项
            if question['type'] in ['SINGLE', 'MULTI']:
                if 'options' not in question or not isinstance(question['options'], list):
                    return False
                if len(question['options']) < 2:
                    return False
        
        return True
    
    def get_survey(self, survey_id):
        """获取问卷信息"""
        result = self.db.execute_query('''
            SELECT s.*, 
                   (SELECT COUNT(*) FROM survey_responses WHERE survey_id = s.id) as response_count
            FROM surveys s
            WHERE s.id = ? AND s.is_active = 1
        ''', (survey_id,))
        
        if result:
            survey = dict(result[0])
            # 解析问题JSON
            if survey['questions']:
                try:
                    survey['questions'] = json.loads(survey['questions'])
                except:
                    survey['questions'] = []
            else:
                survey['questions'] = []
            
            return survey
        return None
    
    def get_channel_surveys(self, channel_id, include_expired=False):
        """获取频道的问卷列表"""
        if include_expired:
            query = '''
                SELECT s.*, 
                       (SELECT COUNT(*) FROM survey_responses WHERE survey_id = s.id) as response_count
                FROM surveys s
                WHERE s.channel_id = ? AND s.is_active = 1
                ORDER BY s.created_at DESC
            '''
        else:
            query = '''
                SELECT s.*, 
                       (SELECT COUNT(*) FROM survey_responses WHERE survey_id = s.id) as response_count
                FROM surveys s
                WHERE s.channel_id = ? AND s.is_active = 1 
                    AND (s.expires_at IS NULL OR s.expires_at > CURRENT_TIMESTAMP)
                ORDER BY s.created_at DESC
            '''
        
        results = self.db.execute_query(query, (channel_id,))
        
        surveys = []
        for row in results:
            survey = dict(row)
            # 解析问题JSON（仅获取基本信息，不包含完整问题内容）
            if survey['questions']:
                try:
                    questions = json.loads(survey['questions'])
                    survey['question_count'] = len(questions)
                except:
                    survey['question_count'] = 0
            else:
                survey['question_count'] = 0
            
            # 移除详细问题内容以减少数据传输
            del survey['questions']
            surveys.append(survey)
        
        return surveys
    
    def submit_response(self, user_id, survey_id, responses):
        """提交问卷回答"""
        # 检查问卷是否存在且有效
        survey = self.get_survey(survey_id)
        if not survey:
            return False, "问卷不存在或已失效"
        
        # 检查是否已过期
        if survey['expires_at']:
            expires_at = datetime.fromisoformat(survey['expires_at'].replace('Z', '+00:00'))
            if datetime.now() > expires_at:
                return False, "问卷已过期"
        
        # 检查是否已经回答过
        existing = self.db.execute_query('''
            SELECT id FROM survey_responses WHERE survey_id = ? AND user_id = ?
        ''', (survey_id, user_id))
        
        if existing:
            return False, "您已经回答过此问卷"
        
        # 验证回答格式
        if not self._validate_responses(survey['questions'], responses):
            return False, "回答格式不正确"
        
        responses_json = json.dumps(responses, ensure_ascii=False)
        
        try:
            self.db.execute_query('''
                INSERT INTO survey_responses (survey_id, user_id, responses)
                VALUES (?, ?, ?)
            ''', (survey_id, user_id, responses_json))
            
            return True, "问卷提交成功"
        except Exception as e:
            return False, f"提交问卷失败: {e}"
    
    def _validate_responses(self, questions, responses):
        """验证回答格式"""
        if not isinstance(responses, list) or len(responses) != len(questions):
            return False
        
        for i, (question, response) in enumerate(zip(questions, responses)):
            question_type = question['type']
            
            # 信息标签类型不需要回答
            if question_type == 'LABEL':
                continue
            
            if question_type == 'SINGLE':
                # 单选题：回答应该是选项索引
                if not isinstance(response, int) or response < 0 or response >= len(question['options']):
                    return False
            
            elif question_type == 'MULTI':
                # 多选题：回答应该是选项索引列表
                if not isinstance(response, list):
                    return False
                for option_index in response:
                    if not isinstance(option_index, int) or option_index < 0 or option_index >= len(question['options']):
                        return False
            
            elif question_type == 'TEXT':
                # 文本题：回答应该是字符串
                if not isinstance(response, str):
                    return False
        
        return True
    
    def get_survey_responses(self, survey_id):
        """获取问卷的所有回答"""
        results = self.db.execute_query('''
            SELECT * FROM survey_responses WHERE survey_id = ? ORDER BY submitted_at
        ''', (survey_id,))
        
        responses = []
        for row in results:
            response = dict(row)
            # 解析回答JSON
            if response['responses']:
                try:
                    response['responses'] = json.loads(response['responses'])
                except:
                    response['responses'] = []
            else:
                response['responses'] = []
            responses.append(response)
        
        return responses
    
    def get_survey_statistics(self, survey_id):
        """获取问卷统计分析"""
        survey = self.get_survey(survey_id)
        if not survey:
            return None
        
        responses = self.get_survey_responses(survey_id)
        if not responses:
            return {
                'total_responses': 0,
                'completion_rate': 0,
                'question_stats': []
            }
        
        total_responses = len(responses)
        questions = survey['questions']
        question_stats = []
        
        for i, question in enumerate(questions):
            question_type = question['type']
            question_stat = {
                'question_index': i,
                'question_title': question['title'],
                'question_type': question_type,
                'type_display': self.QUESTION_TYPES[question_type]
            }
            
            if question_type == 'LABEL':
                # 信息标签不需要统计
                question_stat['stats'] = None
            
            elif question_type == 'SINGLE':
                # 单选题统计
                option_counts = Counter()
                valid_responses = 0
                
                for response in responses:
                    if i < len(response['responses']) and response['responses'][i] is not None:
                        option_index = response['responses'][i]
                        if 0 <= option_index < len(question['options']):
                            option_counts[option_index] += 1
                            valid_responses += 1
                
                stats = []
                for j, option in enumerate(question['options']):
                    count = option_counts.get(j, 0)
                    percentage = (count / valid_responses * 100) if valid_responses > 0 else 0
                    stats.append({
                        'option': option,
                        'count': count,
                        'percentage': round(percentage, 2)
                    })
                
                question_stat['stats'] = {
                    'type': 'single_choice',
                    'valid_responses': valid_responses,
                    'options': stats
                }
            
            elif question_type == 'MULTI':
                # 多选题统计
                option_counts = Counter()
                valid_responses = 0
                
                for response in responses:
                    if i < len(response['responses']) and response['responses'][i] is not None:
                        selected_options = response['responses'][i]
                        if isinstance(selected_options, list):
                            valid_responses += 1
                            for option_index in selected_options:
                                if 0 <= option_index < len(question['options']):
                                    option_counts[option_index] += 1
                
                stats = []
                for j, option in enumerate(question['options']):
                    count = option_counts.get(j, 0)
                    percentage = (count / valid_responses * 100) if valid_responses > 0 else 0
                    stats.append({
                        'option': option,
                        'count': count,
                        'percentage': round(percentage, 2)
                    })
                
                question_stat['stats'] = {
                    'type': 'multiple_choice',
                    'valid_responses': valid_responses,
                    'options': stats
                }
            
            elif question_type == 'TEXT':
                # 文本题统计
                text_responses = []
                for response in responses:
                    if i < len(response['responses']) and response['responses'][i]:
                        text_responses.append(response['responses'][i])
                
                question_stat['stats'] = {
                    'type': 'text',
                    'valid_responses': len(text_responses),
                    'responses': text_responses[:50]  # 最多返回50个回答用于预览
                }
            
            question_stats.append(question_stat)
        
        return {
            'total_responses': total_responses,
            'completion_rate': 100,  # 提交即完成
            'question_stats': question_stats
        }
    
    def export_survey_data(self, survey_id, format='excel'):
        """导出问卷数据"""
        survey = self.get_survey(survey_id)
        if not survey:
            return None, "问卷不存在"
        
        responses = self.get_survey_responses(survey_id)
        if not responses:
            return None, "暂无回答数据"
        
        try:
            # 准备数据
            data = []
            questions = survey['questions']
            
            for response in responses:
                row = {
                    '用户ID': response['user_id'],
                    '提交时间': response['submitted_at']
                }
                
                for i, question in enumerate(questions):
                    question_title = question['title']
                    question_type = question['type']
                    
                    if question_type == 'LABEL':
                        continue
                    
                    if i < len(response['responses']):
                        answer = response['responses'][i]
                        
                        if question_type == 'SINGLE':
                            if isinstance(answer, int) and 0 <= answer < len(question['options']):
                                row[question_title] = question['options'][answer]
                            else:
                                row[question_title] = ''
                        
                        elif question_type == 'MULTI':
                            if isinstance(answer, list):
                                selected = [question['options'][idx] for idx in answer 
                                          if isinstance(idx, int) and 0 <= idx < len(question['options'])]
                                row[question_title] = '; '.join(selected)
                            else:
                                row[question_title] = ''
                        
                        elif question_type == 'TEXT':
                            row[question_title] = str(answer) if answer else ''
                    else:
                        row[question_title] = ''
                
                data.append(row)
            
            # 创建DataFrame
            df = pd.DataFrame(data)
            
            if format == 'excel':
                filename = f"survey_{survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
                return df, filename
            elif format == 'csv':
                filename = f"survey_{survey_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                return df, filename
            else:
                return None, "不支持的导出格式"
        
        except Exception as e:
            return None, f"导出数据失败: {e}"
    
    def delete_survey(self, user_id, survey_id):
        """删除问卷（仅创建者可删除）"""
        # 检查权限
        survey = self.db.execute_query('''
            SELECT created_by FROM surveys WHERE id = ? AND is_active = 1
        ''', (survey_id,))
        
        if not survey or survey[0]['created_by'] != user_id:
            return False, "无权限删除该问卷"
        
        try:
            # 软删除问卷
            self.db.execute_query('''
                UPDATE surveys SET is_active = 0 WHERE id = ?
            ''', (survey_id,))
            
            return True, "问卷删除成功"
        except Exception as e:
            return False, f"删除问卷失败: {e}"

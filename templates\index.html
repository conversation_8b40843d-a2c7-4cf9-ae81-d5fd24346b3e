{% extends "base.html" %}

{% block title %}首页 - 多身份聊天室系统{% endblock %}

{% block content %}
<div class="row">
    <!-- 左侧：频道列表 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list"></i> 频道列表
                </h5>
                <button class="btn btn-sm btn-primary" onclick="showCreateChannelModal()">
                    <i class="fas fa-plus"></i> 创建频道
                </button>
            </div>
            <div class="card-body">
                <!-- 我的频道 -->
                <h6><i class="fas fa-user"></i> 我的频道</h6>
                <div id="myChannels" class="mb-3">
                    <!-- 动态加载 -->
                </div>
                
                <!-- 公开频道 -->
                <h6><i class="fas fa-globe"></i> 公开频道</h6>
                <div id="publicChannels">
                    <!-- 动态加载 -->
                </div>
            </div>
        </div>
    </div>
    
    <!-- 右侧：欢迎信息和统计 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-body text-center">
                <h2><i class="fas fa-comments text-primary"></i></h2>
                <h4>欢迎来到多身份聊天室系统</h4>
                <p class="text-muted">在这里，您可以以不同的身份与他人交流，体验多元化的社交互动。</p>
                
                <div class="row mt-4">
                    <div class="col-md-3">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 id="totalChannels">-</h5>
                                <small>总频道数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 id="onlineUsers">-</h5>
                                <small>在线用户</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-info text-white">
                            <div class="card-body">
                                <h5 id="totalMessages">-</h5>
                                <small>总消息数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h5 id="activeSurveys">-</h5>
                                <small>活跃问卷</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 功能介绍 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> 功能介绍</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-user-tag text-primary"></i> 多身份系统</h6>
                        <p class="small text-muted">每个用户可以设置最多3个身份，在不同频道中选择性公开，体验多元化交流。</p>
                        
                        <h6><i class="fas fa-lock text-success"></i> 频道权限</h6>
                        <p class="small text-muted">支持公开、密码保护和邀请制三种频道类型，满足不同的隐私需求。</p>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-poll text-info"></i> 问卷调查</h6>
                        <p class="small text-muted">在频道中发布问卷，收集意见和数据，支持多种题型和统计分析。</p>
                        
                        <h6><i class="fas fa-image text-warning"></i> 图片分享</h6>
                        <p class="small text-muted">支持图片上传和分享，自动生成缩略图，优化浏览体验。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 创建频道模态框 -->
<div class="modal fade" id="createChannelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus"></i> 创建频道
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createChannelForm">
                    <div class="mb-3">
                        <label class="form-label">频道名称</label>
                        <input type="text" class="form-control" id="channelName" required maxlength="50">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">频道类型</label>
                        <select class="form-select" id="channelType" onchange="togglePasswordField()">
                            <option value="public">公开频道</option>
                            <option value="password">密码频道</option>
                            <option value="invite">邀请制频道</option>
                        </select>
                    </div>
                    
                    <div class="mb-3" id="passwordField" style="display: none;">
                        <label class="form-label">频道密码</label>
                        <input type="password" class="form-control" id="channelPassword">
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">最大成员数（0表示无限制）</label>
                        <input type="number" class="form-control" id="maxMembers" value="0" min="0" max="1000">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="createChannel()">创建频道</button>
            </div>
        </div>
    </div>
</div>

<!-- 加入频道模态框 -->
<div class="modal fade" id="joinChannelModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-sign-in-alt"></i> 加入频道
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="joinChannelInfo">
                    <!-- 动态内容 -->
                </div>
                <form id="joinChannelForm">
                    <input type="hidden" id="joinChannelId">
                    <div class="mb-3" id="joinPasswordField" style="display: none;">
                        <label class="form-label">频道密码</label>
                        <input type="password" class="form-control" id="joinChannelPassword">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="joinChannel()">加入频道</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/index.js') }}"></script>
{% endblock %}

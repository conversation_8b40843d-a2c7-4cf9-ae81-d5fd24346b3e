@echo off
echo 正在启动多身份聊天室系统...
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python，请先安装Python 3.10+
    pause
    exit /b 1
)

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 激活虚拟环境...
call venv\Scripts\activate

REM 安装依赖
echo 安装依赖包...
pip install -r requirements.txt

REM 创建必要的目录
if not exist "uploads" mkdir uploads
if not exist "database" mkdir database
if not exist "backups" mkdir backups

REM 启动应用
echo.
echo 启动应用...
echo 访问地址：http://127.0.0.1:5000
echo 按 Ctrl+C 停止服务器
echo.
python app.py

pause
